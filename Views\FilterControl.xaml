<UserControl x:Class="TeklaList.Views.FilterControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:TeklaList.Views"
             mc:Ignorable="d"
             d:DesignHeight="300" d:DesignWidth="200">
    <UserControl.Resources>
        <Style x:Key="FilterIconStyle" TargetType="Path">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Stretch" Value="Uniform"/>
            <Setter Property="Data" Value="M8,2 L14,2 L14,3 L13,4 L9,8 L9,14 L8,16 L7,16 L6,14 L6,8 L2,4 L1,3 L1,2 L8,2 Z"/>
            <Setter Property="Stroke" Value="Gray"/>
            <Setter Property="StrokeThickness" Value="0.5"/>
            <Setter Property="Fill" Value="Gray"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=IsFiltered}" Value="True">
                    <Setter Property="Fill" Value="Red"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Button x:Name="FilterButton" Click="FilterButton_Click" ToolTip="点击筛选数据"
                Background="Transparent" BorderThickness="0" Padding="0" Width="16" Height="16" Margin="0">
            <Grid>
                <!-- 漏斗图标 -->
                <Path Style="{StaticResource FilterIconStyle}"/>
            </Grid>
        </Button>

        <Popup x:Name="FilterPopup" StaysOpen="False" Placement="Bottom" AllowsTransparency="True">
            <Border BorderBrush="Gray" BorderThickness="1" Background="White" MinWidth="200" MaxWidth="400">
                <StackPanel Margin="5">
                    <TextBox x:Name="SearchTextBox" Margin="0,0,0,5" TextChanged="SearchTextBox_TextChanged">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                            <Grid>
                                                <TextBox Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                         x:Name="textSource"
                                                         Background="Transparent"
                                                         Panel.ZIndex="2" />
                                                <TextBlock Text="搜索..."
                                                           Visibility="{Binding Path=Text.IsEmpty, RelativeSource={RelativeSource TemplatedParent}}"
                                                           Background="{TemplateBinding Background}"
                                                           Panel.ZIndex="1"
                                                           Foreground="DarkGray"
                                                           Margin="5,1,0,0"/>
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <TextBlock x:Name="ItemsCountText" Text="准备中..." Margin="0,0,0,5" Foreground="Gray" />

                    <ListBox x:Name="ItemsListBox" MaxHeight="300" SelectionMode="Multiple"
                             MinHeight="100" BorderThickness="1" BorderBrush="LightGray">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <CheckBox Content="{Binding Text}" IsChecked="{Binding IsSelected, Mode=TwoWay}" Margin="2" />
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5,0,0">
                        <Button Content="全选" Click="SelectAll_Click" Margin="0,0,5,0" Padding="5,2" />
                        <Button Content="清除" Click="ClearAll_Click" Margin="0,0,5,0" Padding="5,2" />
                        <Button Content="确定" Click="Apply_Click" Padding="5,2" />
                    </StackPanel>
                </StackPanel>
            </Border>
        </Popup>
    </Grid>
</UserControl>

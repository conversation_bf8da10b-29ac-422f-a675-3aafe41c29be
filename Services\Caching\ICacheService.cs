using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TeklaList.Services.Caching
{
    /// <summary>
    /// 缓存服务接口
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// 获取缓存项
        /// </summary>
        T? Get<T>(string key) where T : class;

        /// <summary>
        /// 异步获取缓存项
        /// </summary>
        Task<T?> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// 设置缓存项
        /// </summary>
        void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// 异步设置缓存项
        /// </summary>
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// 获取或创建缓存项
        /// </summary>
        T GetOrCreate<T>(string key, Func<T> factory, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// 异步获取或创建缓存项
        /// </summary>
        Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// 移除缓存项
        /// </summary>
        bool Remove(string key);

        /// <summary>
        /// 异步移除缓存项
        /// </summary>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        void Clear();

        /// <summary>
        /// 异步清空所有缓存
        /// </summary>
        Task ClearAsync();

        /// <summary>
        /// 检查缓存项是否存在
        /// </summary>
        bool Exists(string key);

        /// <summary>
        /// 异步检查缓存项是否存在
        /// </summary>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// 获取所有缓存键
        /// </summary>
        IEnumerable<string> GetKeys();

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        CacheStatistics GetStatistics();

        /// <summary>
        /// 设置缓存大小限制
        /// </summary>
        void SetSizeLimit(int maxItems);

        /// <summary>
        /// 清理过期的缓存项
        /// </summary>
        int CleanupExpired();
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// 缓存项总数
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// 命中次数
        /// </summary>
        public long HitCount { get; set; }

        /// <summary>
        /// 未命中次数
        /// </summary>
        public long MissCount { get; set; }

        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRate => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests => HitCount + MissCount;

        /// <summary>
        /// 过期项数量
        /// </summary>
        public int ExpiredItems { get; set; }

        /// <summary>
        /// 内存使用量（估算，字节）
        /// </summary>
        public long EstimatedMemoryUsage { get; set; }

        /// <summary>
        /// 最后清理时间
        /// </summary>
        public DateTime? LastCleanupTime { get; set; }
    }
}

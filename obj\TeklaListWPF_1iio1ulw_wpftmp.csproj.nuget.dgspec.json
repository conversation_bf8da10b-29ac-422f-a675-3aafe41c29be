{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\TeklaListWPF.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\TeklaListWPF.csproj": {"version": "2.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\TeklaListWPF.csproj", "projectName": "TeklaList", "projectPath": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\TeklaListWPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\TeklaListWPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[2.12.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}
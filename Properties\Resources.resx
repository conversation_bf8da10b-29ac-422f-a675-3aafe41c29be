<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" msdata:Ordinal="5" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>TeklaList</value>
    <comment>应用程序名称</comment>
  </data>
  <data name="AppDescription" xml:space="preserve">
    <value>Tekla Structures 零件列表工具</value>
    <comment>应用程序描述</comment>
  </data>
  <data name="AppVersion" xml:space="preserve">
    <value>2.0.0</value>
    <comment>应用程序版本</comment>
  </data>
  <data name="LoadParts" xml:space="preserve">
    <value>加载零件</value>
    <comment>加载零件按钮文本</comment>
  </data>
  <data name="Refresh" xml:space="preserve">
    <value>刷新</value>
    <comment>刷新按钮文本</comment>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>清除</value>
    <comment>清除按钮文本</comment>
  </data>
  <data name="AssemblyMode" xml:space="preserve">
    <value>构件模式</value>
    <comment>构件模式开关文本</comment>
  </data>
  <data name="PartMode" xml:space="preserve">
    <value>零件模式</value>
    <comment>零件模式文本</comment>
  </data>
  <data name="Connected" xml:space="preserve">
    <value>已连接</value>
    <comment>连接状态：已连接</comment>
  </data>
  <data name="Disconnected" xml:space="preserve">
    <value>未连接</value>
    <comment>连接状态：未连接</comment>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>加载中...</value>
    <comment>加载状态文本</comment>
  </data>
  <data name="Ready" xml:space="preserve">
    <value>就绪</value>
    <comment>就绪状态文本</comment>
  </data>
  <data name="Error" xml:space="preserve">
    <value>错误</value>
    <comment>错误标题</comment>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>警告</value>
    <comment>警告标题</comment>
  </data>
  <data name="Information" xml:space="preserve">
    <value>信息</value>
    <comment>信息标题</comment>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>确认</value>
    <comment>确认标题</comment>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>搜索零件...</value>
    <comment>搜索框占位符文本</comment>
  </data>
  <data name="ClearFilter" xml:space="preserve">
    <value>清除筛选</value>
    <comment>清除筛选按钮文本</comment>
  </data>
  <data name="TotalItems" xml:space="preserve">
    <value>总计: {0} 项</value>
    <comment>总项目数显示格式</comment>
  </data>
  <data name="SelectedItems" xml:space="preserve">
    <value>已选择: {0} 项</value>
    <comment>已选择项目数显示格式</comment>
  </data>
  <data name="LoadingParts" xml:space="preserve">
    <value>正在加载零件...</value>
    <comment>加载零件状态文本</comment>
  </data>
  <data name="LoadingAssemblies" xml:space="preserve">
    <value>正在加载构件...</value>
    <comment>加载构件状态文本</comment>
  </data>
  <data name="PartsLoaded" xml:space="preserve">
    <value>已加载 {0} 个零件</value>
    <comment>零件加载完成状态文本</comment>
  </data>
  <data name="AssembliesLoaded" xml:space="preserve">
    <value>已加载 {0} 个构件</value>
    <comment>构件加载完成状态文本</comment>
  </data>
  <data name="NoPartsFound" xml:space="preserve">
    <value>未找到零件</value>
    <comment>未找到零件提示</comment>
  </data>
  <data name="NoAssembliesFound" xml:space="preserve">
    <value>未找到构件</value>
    <comment>未找到构件提示</comment>
  </data>
  <data name="TeklaNotConnected" xml:space="preserve">
    <value>未连接到 Tekla Structures</value>
    <comment>Tekla未连接错误信息</comment>
  </data>
  <data name="TeklaConnectionError" xml:space="preserve">
    <value>连接 Tekla Structures 时发生错误</value>
    <comment>Tekla连接错误信息</comment>
  </data>
  <data name="LoadError" xml:space="preserve">
    <value>加载数据时发生错误</value>
    <comment>数据加载错误信息</comment>
  </data>
  <data name="HighlightError" xml:space="preserve">
    <value>高亮显示时发生错误</value>
    <comment>高亮显示错误信息</comment>
  </data>
  <data name="Index" xml:space="preserve">
    <value>序号</value>
    <comment>序号列标题</comment>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名称</value>
    <comment>名称列标题</comment>
  </data>
  <data name="PartNumber" xml:space="preserve">
    <value>零件号</value>
    <comment>零件号列标题</comment>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>规格</value>
    <comment>规格列标题</comment>
  </data>
  <data name="Material" xml:space="preserve">
    <value>材质</value>
    <comment>材质列标题</comment>
  </data>
  <data name="Finish" xml:space="preserve">
    <value>表面处理</value>
    <comment>表面处理列标题</comment>
  </data>
  <data name="AssemblyNumber" xml:space="preserve">
    <value>构件号</value>
    <comment>构件号列标题</comment>
  </data>
  <data name="MainPartName" xml:space="preserve">
    <value>主零件名称</value>
    <comment>主零件名称列标题</comment>
  </data>
  <data name="PartCount" xml:space="preserve">
    <value>零件数量</value>
    <comment>零件数量列标题</comment>
  </data>
  <data name="Count" xml:space="preserve">
    <value>数量</value>
    <comment>数量列标题</comment>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>零件</value>
    <comment>零件标签页标题</comment>
  </data>
  <data name="MergedParts" xml:space="preserve">
    <value>合并零件</value>
    <comment>合并零件标签页标题</comment>
  </data>
  <data name="Assemblies" xml:space="preserve">
    <value>构件</value>
    <comment>构件标签页标题</comment>
  </data>
  <data name="ApplicationStarting" xml:space="preserve">
    <value>应用程序正在启动...</value>
    <comment>应用程序启动状态</comment>
  </data>
  <data name="ApplicationReady" xml:space="preserve">
    <value>应用程序已就绪</value>
    <comment>应用程序就绪状态</comment>
  </data>
  <data name="CheckingTeklaConnection" xml:space="preserve">
    <value>正在检查 Tekla 连接...</value>
    <comment>检查Tekla连接状态</comment>
  </data>
  <data name="TeklaConnected" xml:space="preserve">
    <value>Tekla Structures 已连接</value>
    <comment>Tekla连接成功状态</comment>
  </data>
  <data name="FilteredItems" xml:space="preserve">
    <value>筛选结果: {0} 项</value>
    <comment>筛选结果数量显示格式</comment>
  </data>
  <data name="ClearSelection" xml:space="preserve">
    <value>清除选择</value>
    <comment>清除选择按钮文本</comment>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>全选</value>
    <comment>全选按钮文本</comment>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>导出数据</value>
    <comment>导出数据按钮文本</comment>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>设置</value>
    <comment>设置按钮文本</comment>
  </data>
  <data name="About" xml:space="preserve">
    <value>关于</value>
    <comment>关于按钮文本</comment>
  </data>
  <data name="Help" xml:space="preserve">
    <value>帮助</value>
    <comment>帮助按钮文本</comment>
  </data>
</root>
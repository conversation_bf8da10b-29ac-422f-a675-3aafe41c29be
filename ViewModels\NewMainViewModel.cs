using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using TeklaList.Configuration;
using TeklaList.Models;
using TeklaList.Services;

namespace TeklaList.ViewModels
{
    /// <summary>
    /// 新的主视图模型，使用依赖注入和现代化架构
    /// </summary>
    public class NewMainViewModel : ViewModelBase
    {
        private readonly ILogger<NewMainViewModel> _logger;
        private readonly ITeklaModelService _teklaModelService;
        private readonly ConfigurationManager _configManager;
        private readonly CancellationTokenSource _cancellationTokenSource;

        // 私有字段
        private bool _isLoading;
        private bool _isAssemblyMode;
        private string _statusText = "就绪";
        private string _countText = "数量: 0";
        private string _timeText = "时间: 0s";
        private string _searchText = string.Empty;
        private bool _isMergeRows;

        // 集合
        private readonly ObservableCollection<TeklaModelPart> _parts;
        private readonly ObservableCollection<AssemblyInfo> _assemblies;
        private ICollectionView? _partsView;
        private ICollectionView? _assembliesView;

        // 命令
        public ICommand LoadAllPartsCommand { get; }
        public ICommand LoadSelectedPartsCommand { get; }
        public ICommand RefreshDataCommand { get; }
        public ICommand ToggleAssemblyModeCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ClearSearchCommand { get; }
        public ICommand ExitCommand { get; }

        public NewMainViewModel(
            ILogger<NewMainViewModel> logger,
            ITeklaModelService teklaModelService,
            ConfigurationManager configManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _teklaModelService = teklaModelService ?? throw new ArgumentNullException(nameof(teklaModelService));
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));

            _cancellationTokenSource = new CancellationTokenSource();

            // 初始化集合
            _parts = new ObservableCollection<TeklaModelPart>();
            _assemblies = new ObservableCollection<AssemblyInfo>();

            // 创建视图
            CreateCollectionViews();

            // 初始化命令
            LoadAllPartsCommand = new AsyncRelayCommand(LoadAllPartsAsync, () => !IsLoading);
            LoadSelectedPartsCommand = new AsyncRelayCommand(LoadSelectedPartsAsync, () => !IsLoading);
            RefreshDataCommand = new AsyncRelayCommand(RefreshDataAsync, () => !IsLoading);
            ToggleAssemblyModeCommand = new RelayCommand(_ => ToggleAssemblyMode());
            SearchCommand = new RelayCommand(_ => ExecuteSearch());
            ClearSearchCommand = new RelayCommand(_ => ClearSearch());
            ExitCommand = new RelayCommand(_ => Application.Current.Shutdown());

            // 绑定配置
            BindToConfiguration();

            // 订阅服务事件
            _teklaModelService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _teklaModelService.ModelChanged += OnModelChanged;

            _logger.LogInformation("主视图模型初始化完成");
        }

        #region 属性

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 是否为构件模式
        /// </summary>
        public bool IsAssemblyMode
        {
            get => _isAssemblyMode;
            set
            {
                if (SetProperty(ref _isAssemblyMode, value))
                {
                    OnPropertyChanged(nameof(CurrentModeText));
                    UpdateCountText();
                }
            }
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            private set => SetProperty(ref _statusText, value);
        }

        /// <summary>
        /// 数量文本
        /// </summary>
        public string CountText
        {
            get => _countText;
            private set => SetProperty(ref _countText, value);
        }

        /// <summary>
        /// 时间文本
        /// </summary>
        public string TimeText
        {
            get => _timeText;
            private set => SetProperty(ref _timeText, value);
        }

        /// <summary>
        /// 搜索文本
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    // 实时搜索
                    ExecuteSearch();
                }
            }
        }

        /// <summary>
        /// 是否合并相同行
        /// </summary>
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set
            {
                if (SetProperty(ref _isMergeRows, value))
                {
                    RefreshViews();
                }
            }
        }

        /// <summary>
        /// 当前模式文本
        /// </summary>
        public string CurrentModeText => IsAssemblyMode ? "零件模式" : "构件模式";

        /// <summary>
        /// 零件集合
        /// </summary>
        public ObservableCollection<TeklaModelPart> Parts => _parts;

        /// <summary>
        /// 构件集合
        /// </summary>
        public ObservableCollection<AssemblyInfo> Assemblies => _assemblies;

        /// <summary>
        /// 零件视图
        /// </summary>
        public ICollectionView? PartsView => _partsView;

        /// <summary>
        /// 构件视图
        /// </summary>
        public ICollectionView? AssembliesView => _assembliesView;

        /// <summary>
        /// Tekla连接状态
        /// </summary>
        public bool IsConnected => _teklaModelService.IsConnected;

        /// <summary>
        /// 应用程序设置
        /// </summary>
        public AppSettings Settings => _configManager.Settings;

        #endregion

        #region 私有方法

        private void CreateCollectionViews()
        {
            _partsView = CollectionViewSource.GetDefaultView(_parts);
            _assembliesView = CollectionViewSource.GetDefaultView(_assemblies);

            // 设置过滤器
            _partsView.Filter = FilterParts;
            _assembliesView.Filter = FilterAssemblies;
        }

        private void BindToConfiguration()
        {
            var settings = _configManager.Settings;

            // 绑定设置到属性
            IsAssemblyMode = false; // 默认零件模式
            IsMergeRows = settings.IsMergeRows;

            // 监听设置变更
            settings.PropertyChanged += OnSettingsChanged;
        }

        private void OnSettingsChanged(object? sender, PropertyChangedEventArgs e)
        {
            // 当设置变更时，更新相关属性
            if (e.PropertyName == nameof(AppSettings.IsMergeRows))
            {
                IsMergeRows = Settings.IsMergeRows;
            }
        }

        private void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                OnPropertyChanged(nameof(IsConnected));
                StatusText = isConnected ? "已连接到Tekla" : "未连接到Tekla";
            });
        }

        private void OnModelChanged(object? sender, EventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                StatusText = "模型已变更，建议刷新数据";
            });
        }

        private bool FilterParts(object item)
        {
            if (!(item is TeklaModelPart part)) return false;
            if (string.IsNullOrWhiteSpace(SearchText)) return true;

            var searchLower = SearchText.ToLowerInvariant();
            return part.Name.ToLowerInvariant().Contains(searchLower) ||
                   part.PartNumber.ToLowerInvariant().Contains(searchLower) ||
                   part.Profile.ToLowerInvariant().Contains(searchLower) ||
                   part.Material.ToLowerInvariant().Contains(searchLower) ||
                   part.AssemblyNumber.ToLowerInvariant().Contains(searchLower);
        }

        private bool FilterAssemblies(object item)
        {
            if (!(item is AssemblyInfo assembly)) return false;
            if (string.IsNullOrWhiteSpace(SearchText)) return true;

            var searchLower = SearchText.ToLowerInvariant();
            return assembly.AssemblyNumber.ToLowerInvariant().Contains(searchLower);
                   // 暂时注释掉这些属性，等AssemblyInfo完善后再启用
                   // assembly.MainPartName.ToLowerInvariant().Contains(searchLower) ||
                   // assembly.MainPartProfile.ToLowerInvariant().Contains(searchLower) ||
                   // assembly.MainPartMaterial.ToLowerInvariant().Contains(searchLower);
        }

        private void RefreshViews()
        {
            _partsView?.Refresh();
            _assembliesView?.Refresh();
            UpdateCountText();
        }

        private void UpdateCountText()
        {
            var count = IsAssemblyMode ? _assemblies.Count : _parts.Count;
            CountText = $"数量: {count:N0}";
        }

        #endregion

        #region 命令实现

        private async Task LoadAllPartsAsync()
        {
            await LoadPartsAsync(() => _teklaModelService.GetAllPartsAsync(_cancellationTokenSource.Token), "加载所有零件");
        }

        private async Task LoadSelectedPartsAsync()
        {
            await LoadPartsAsync(() => _teklaModelService.GetSelectedPartsAsync(_cancellationTokenSource.Token), "加载选中零件");
        }

        private async Task LoadPartsAsync(Func<Task<System.Collections.Generic.List<TeklaModelPart>>> loadFunc, string operation)
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = $"正在{operation}...";
                var startTime = DateTime.Now;

                // using var _ = _logger.LogExecutionTime(operation); // 暂时注释掉

                var parts = await loadFunc();

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _parts.Clear();
                    foreach (var part in parts)
                    {
                        _parts.Add(part);
                    }

                    if (IsAssemblyMode)
                    {
                        _ = Task.Run(async () =>
                        {
                            var assemblies = await _teklaModelService.GetAssembliesAsync(parts, _cancellationTokenSource.Token);
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                _assemblies.Clear();
                                foreach (var assembly in assemblies)
                                {
                                    _assemblies.Add(assembly);
                                }
                                UpdateCountText();
                            });
                        });
                    }
                    else
                    {
                        UpdateCountText();
                    }

                    var elapsed = DateTime.Now - startTime;
                    TimeText = $"时间: {elapsed.TotalSeconds:F1}s";
                    StatusText = $"{operation}完成";
                });

                _logger.LogInformation("{Operation}完成，获取到 {Count} 个零件", operation, parts.Count);
            }
            catch (OperationCanceledException)
            {
                StatusText = $"{operation}已取消";
                _logger.LogInformation("{Operation}被用户取消", operation);
            }
            catch (Exception ex)
            {
                StatusText = $"{operation}失败: {ex.Message}";
                _logger.LogError(ex, "{Operation}时发生错误", operation);
                MessageBox.Show($"{operation}时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RefreshDataAsync()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "正在刷新数据...";

                await _teklaModelService.RefreshModelAsync(_cancellationTokenSource.Token);

                // 重新加载当前数据
                if (_parts.Count > 0)
                {
                    await LoadAllPartsAsync();
                }

                StatusText = "数据刷新完成";
                _logger.LogInformation("数据刷新完成");
            }
            catch (Exception ex)
            {
                StatusText = $"刷新失败: {ex.Message}";
                _logger.LogError(ex, "刷新数据时发生错误");
                MessageBox.Show($"刷新数据时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ToggleAssemblyMode()
        {
            IsAssemblyMode = !IsAssemblyMode;
            _logger.LogDebug("切换到{Mode}模式", IsAssemblyMode ? "构件" : "零件");
        }

        private void ExecuteSearch()
        {
            RefreshViews();
        }

        private void ClearSearch()
        {
            SearchText = string.Empty;
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();

                if (_configManager.Settings != null)
                {
                    _configManager.Settings.PropertyChanged -= OnSettingsChanged;
                }

                _teklaModelService.ConnectionStatusChanged -= OnConnectionStatusChanged;
                _teklaModelService.ModelChanged -= OnModelChanged;
            }

            base.Dispose(disposing);
        }
    }
}

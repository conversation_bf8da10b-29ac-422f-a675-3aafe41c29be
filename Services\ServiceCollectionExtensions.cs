using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TeklaList.Configuration;
using TeklaList.Services.Caching;
using TeklaList.Services.Logging;
using TeklaList.ViewModels;

namespace TeklaList.Services
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 注册TeklaList核心服务
        /// </summary>
        public static IServiceCollection AddTeklaListServices(this IServiceCollection services)
        {
            // 配置管理
            services.AddSingleton<ConfigurationManager>();

            // 缓存服务
            services.AddSingleton<ICacheService, MemoryCacheService>();

            // Tekla模型服务
            services.AddSingleton<ITeklaModelService, HighPerformanceTeklaModelService>();

            // ViewModels
            services.AddTransient<MainViewModel>();
            services.AddTransient<PartListViewModel>();
            services.AddTransient<AssemblyListViewModel>();

            return services;
        }

        /// <summary>
        /// 配置日志服务
        /// </summary>
        public static IServiceCollection AddTeklaListLogging(this IServiceCollection services, string logDirectory)
        {
            // 初始化日志系统
            LoggingService.Initialize(logDirectory);

            // 添加日志服务
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            return services;
        }

        /// <summary>
        /// 验证服务注册
        /// </summary>
        public static void ValidateServices(this IServiceProvider serviceProvider)
        {
            // 验证关键服务是否正确注册
            var logger = serviceProvider.GetService<ILogger<ServiceCollectionExtensions>>();
            
            try
            {
                var configManager = serviceProvider.GetRequiredService<ConfigurationManager>();
                logger?.LogInformation("配置管理器服务验证成功");

                var cacheService = serviceProvider.GetRequiredService<ICacheService>();
                logger?.LogInformation("缓存服务验证成功");

                var teklaService = serviceProvider.GetRequiredService<ITeklaModelService>();
                logger?.LogInformation("Tekla模型服务验证成功");

                var mainViewModel = serviceProvider.GetRequiredService<MainViewModel>();
                logger?.LogInformation("主视图模型验证成功");

                logger?.LogInformation("所有核心服务验证完成");
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "服务验证失败");
                throw;
            }
        }
    }
}

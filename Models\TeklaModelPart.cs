using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;

namespace TeklaList.Models
{
    /// <summary>
    /// Tekla模型零件数据模型
    /// </summary>
    public class TeklaModelPart : INotifyPropertyChanged, IEquatable<TeklaModelPart>, ICloneable
    {
        private int _index;
        private string _name;
        private string _partNumber;
        private string _profile;
        private string _finish;
        private int _boltCount;
        private string _assemblyNumber;
        private string _assemblyPrefix;
        private string _assemblyStartNumber;
        private string _partPrefix;
        private string _partStartNumber;
        private string _guid;
        private int? _modelObjectId;
        private bool _isMainPart;
        private string _boughtItem;
        private string _material;
        private string _class;
        private string _phase;
        private string _remark;
        private string _assemblyNumberDisplay;
        private int _count = 1;

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        [JsonProperty("index")]
        public int Index
        {
            get => _index;
            set
            {
                if (value < 0)
                    throw new ArgumentException("Index cannot be negative");

                if (_index != value)
                {
                    _index = value;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("name")]
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("partNumber")]
        public string PartNumber
        {
            get => _partNumber;
            set
            {
                if (_partNumber != value)
                {
                    _partNumber = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("profile")]
        public string Profile
        {
            get => _profile;
            set
            {
                if (_profile != value)
                {
                    _profile = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("finish")]
        public string Finish
        {
            get => _finish;
            set
            {
                if (_finish != value)
                {
                    _finish = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("boltCount")]
        public int BoltCount
        {
            get => _boltCount;
            set
            {
                if (_boltCount != value)
                {
                    _boltCount = value;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("assemblyNumber")]
        public string AssemblyNumber
        {
            get => _assemblyNumber;
            set
            {
                if (_assemblyNumber != value)
                {
                    _assemblyNumber = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("assemblyPrefix")]
        public string AssemblyPrefix
        {
            get => _assemblyPrefix;
            set
            {
                if (_assemblyPrefix != value)
                {
                    _assemblyPrefix = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("assemblyStartNumber")]
        public string AssemblyStartNumber
        {
            get => _assemblyStartNumber;
            set
            {
                if (_assemblyStartNumber != value)
                {
                    _assemblyStartNumber = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("partPrefix")]
        public string PartPrefix
        {
            get => _partPrefix;
            set
            {
                if (_partPrefix != value)
                {
                    _partPrefix = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("partStartNumber")]
        public string PartStartNumber
        {
            get => _partStartNumber;
            set
            {
                if (_partStartNumber != value)
                {
                    _partStartNumber = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("guid")]
        public string Guid
        {
            get => _guid;
            set
            {
                if (_guid != value)
                {
                    _guid = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("modelObjectId")]
        public int? ModelObjectId
        {
            get => _modelObjectId;
            set
            {
                if (_modelObjectId != value)
                {
                    _modelObjectId = value;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("isMainPart")]
        public bool IsMainPart
        {
            get => _isMainPart;
            set
            {
                if (_isMainPart != value)
                {
                    _isMainPart = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(MainPartDisplay));
                }
            }
        }

        [JsonProperty("mainPartDisplay")]
        public string MainPartDisplay
        {
            get
            {
                string str = IsMainPart ? "Main" : "Secd";
                return BoughtItem == "Yes" ? str + "/BOI" : str;
            }
        }

        [JsonProperty("assemblyNumberDisplay")]
        public string AssemblyNumberDisplay
        {
            get => _assemblyNumberDisplay;
            set
            {
                if (_assemblyNumberDisplay != value)
                {
                    _assemblyNumberDisplay = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("boughtItem")]
        public string BoughtItem
        {
            get => _boughtItem;
            set
            {
                if (_boughtItem != value)
                {
                    _boughtItem = value ?? string.Empty;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(MainPartDisplay));
                }
            }
        }

        [JsonProperty("material")]
        public string Material
        {
            get => _material;
            set
            {
                if (_material != value)
                {
                    _material = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("class")]
        public string Class
        {
            get => _class;
            set
            {
                if (_class != value)
                {
                    _class = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("phase")]
        public string Phase
        {
            get => _phase;
            set
            {
                if (_phase != value)
                {
                    _phase = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("remark")]
        public string Remark
        {
            get => _remark;
            set
            {
                if (_remark != value)
                {
                    _remark = value ?? string.Empty;
                    OnPropertyChanged();
                }
            }
        }

        [JsonProperty("count")]
        public int Count
        {
            get => _count;
            set
            {
                if (_count != value)
                {
                    _count = value;
                    OnPropertyChanged();
                }
            }
        }

        public TeklaModelPart() { }

        public TeklaModelPart(
            int index,
            string name = "",
            string partNumber = "",
            string profile = "",
            string material = "",
            string finish = "",
            string classValue = "",
            string phase = "",
            string boughtItem = "",
            int boltCount = 0,
            string assemblyNumber = "",
            string assemblyPrefix = "",
            string assemblyStartNumber = "",
            string partPrefix = "",
            string partStartNumber = "",
            string guid = "",
            int? modelObjectId = null,
            bool isMainPart = false,
            string remark = "")
        {
            Index = index;
            Name = name ?? string.Empty;
            PartNumber = partNumber ?? string.Empty;
            Profile = profile ?? string.Empty;
            Material = material ?? string.Empty;
            Finish = finish ?? string.Empty;
            Class = classValue ?? string.Empty;
            Phase = phase ?? string.Empty;
            BoughtItem = boughtItem ?? string.Empty;
            BoltCount = boltCount;
            AssemblyNumber = assemblyNumber ?? string.Empty;
            AssemblyPrefix = assemblyPrefix ?? string.Empty;
            AssemblyStartNumber = assemblyStartNumber ?? string.Empty;
            PartPrefix = partPrefix ?? string.Empty;
            PartStartNumber = partStartNumber ?? string.Empty;
            Guid = guid ?? string.Empty;
            ModelObjectId = modelObjectId;
            IsMainPart = isMainPart;
            Remark = remark ?? string.Empty;
        }

        public override string ToString()
        {
            return string.Format("零件 #{0}: {1} ({2}) - 材质: {3}, 零件号: {4}", Index, Name, Profile, Material, PartNumber);
        }

        #region IEquatable<TeklaModelPart> 实现

        public bool Equals(TeklaModelPart? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;

            return ModelObjectId == other.ModelObjectId &&
                   string.Equals(Guid, other.Guid, StringComparison.OrdinalIgnoreCase);
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as TeklaModelPart);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hash = 17;
                hash = hash * 23 + (ModelObjectId?.GetHashCode() ?? 0);
                hash = hash * 23 + (Guid?.ToLowerInvariant().GetHashCode() ?? 0);
                return hash;
            }
        }

        public static bool operator ==(TeklaModelPart? left, TeklaModelPart? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(TeklaModelPart? left, TeklaModelPart? right)
        {
            return !Equals(left, right);
        }

        #endregion

        #region ICloneable 实现

        public object Clone()
        {
            return new TeklaModelPart(
                Index, Name, PartNumber, Profile, Material, Finish, Class, Phase,
                BoughtItem, BoltCount, AssemblyNumber, AssemblyPrefix, AssemblyStartNumber,
                PartPrefix, PartStartNumber, Guid, ModelObjectId, IsMainPart, Remark)
            {
                Count = Count
            };
        }

        /// <summary>
        /// 创建深拷贝
        /// </summary>
        public TeklaModelPart DeepClone()
        {
            return (TeklaModelPart)Clone();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查零件是否有效
        /// </summary>
        [JsonIgnore]
        public bool IsValid => !string.IsNullOrWhiteSpace(Name) && !string.IsNullOrWhiteSpace(PartNumber);

        /// <summary>
        /// 获取显示名称
        /// </summary>
        [JsonIgnore]
        public string DisplayName => string.IsNullOrWhiteSpace(Name) ? PartNumber : Name;

        /// <summary>
        /// 获取完整描述
        /// </summary>
        [JsonIgnore]
        public string FullDescription => $"{DisplayName} ({Profile}) - {Material}";

        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void Reset()
        {
            Index = 0;
            Name = string.Empty;
            PartNumber = string.Empty;
            Profile = string.Empty;
            Material = string.Empty;
            Finish = string.Empty;
            Class = string.Empty;
            Phase = string.Empty;
            BoughtItem = string.Empty;
            BoltCount = 0;
            AssemblyNumber = string.Empty;
            AssemblyPrefix = string.Empty;
            AssemblyStartNumber = string.Empty;
            PartPrefix = string.Empty;
            PartStartNumber = string.Empty;
            Guid = string.Empty;
            ModelObjectId = null;
            IsMainPart = false;
            Remark = string.Empty;
            Count = 1;
        }

        /// <summary>
        /// 从另一个零件复制数据
        /// </summary>
        public void CopyFrom(TeklaModelPart other)
        {
            if (other == null) throw new ArgumentNullException(nameof(other));

            Index = other.Index;
            Name = other.Name;
            PartNumber = other.PartNumber;
            Profile = other.Profile;
            Material = other.Material;
            Finish = other.Finish;
            Class = other.Class;
            Phase = other.Phase;
            BoughtItem = other.BoughtItem;
            BoltCount = other.BoltCount;
            AssemblyNumber = other.AssemblyNumber;
            AssemblyPrefix = other.AssemblyPrefix;
            AssemblyStartNumber = other.AssemblyStartNumber;
            PartPrefix = other.PartPrefix;
            PartStartNumber = other.PartStartNumber;
            Guid = other.Guid;
            ModelObjectId = other.ModelObjectId;
            IsMainPart = other.IsMainPart;
            Remark = other.Remark;
            Count = other.Count;
        }

        #endregion
    }
}

using System;
using System.IO;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace TeklaList.Services.Logging
{
    /// <summary>
    /// 日志服务
    /// </summary>
    public static class LoggingService
    {
        private static ILoggerFactory? _loggerFactory;
        private static bool _isInitialized;

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        public static void Initialize(string logDirectory)
        {
            if (_isInitialized) return;

            try
            {
                // 确保日志目录存在
                Directory.CreateDirectory(logDirectory);

                // 配置Serilog
                var logFilePath = Path.Combine(logDirectory, "TeklaList-.log");

                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                    .MinimumLevel.Override("System", LogEventLevel.Warning)
                    .Enrich.FromLogContext()
                    .WriteTo.Console(
                        outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                    .WriteTo.File(
                        logFilePath,
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 30,
                        outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}",
                        shared: true)
                    .CreateLogger();

                // 创建日志工厂
                _loggerFactory = LoggerFactory.Create(builder =>
                {
                    builder.AddSerilog(Log.Logger);
                });

                _isInitialized = true;

                var logger = GetLogger<LoggingService>();
                logger.LogInformation("日志系统初始化成功，日志目录: {LogDirectory}", logDirectory);
            }
            catch (Exception ex)
            {
                // 如果日志初始化失败，至少要能记录到控制台
                Console.WriteLine($"日志系统初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取指定类型的日志记录器
        /// </summary>
        public static ILogger<T> GetLogger<T>()
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("日志系统尚未初始化，请先调用 Initialize 方法");
            }

            return _loggerFactory!.CreateLogger<T>();
        }

        /// <summary>
        /// 获取指定名称的日志记录器
        /// </summary>
        public static ILogger GetLogger(string categoryName)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("日志系统尚未初始化，请先调用 Initialize 方法");
            }

            return _loggerFactory!.CreateLogger(categoryName);
        }

        /// <summary>
        /// 关闭日志系统
        /// </summary>
        public static void Shutdown()
        {
            try
            {
                if (_isInitialized)
                {
                    var logger = GetLogger<LoggingService>();
                    logger.LogInformation("日志系统正在关闭...");
                }
            }
            catch
            {
                // 忽略关闭时的异常
            }
            finally
            {
                _loggerFactory?.Dispose();
                Log.CloseAndFlush();
                _isInitialized = false;
            }
        }
    }

    /// <summary>
    /// 性能监控日志扩展
    /// </summary>
    public static class PerformanceLoggingExtensions
    {
        /// <summary>
        /// 记录操作执行时间
        /// </summary>
        public static IDisposable LogExecutionTime(this ILogger logger, string operationName)
        {
            return new ExecutionTimeLogger(logger, operationName);
        }

        private class ExecutionTimeLogger : IDisposable
        {
            private readonly ILogger _logger;
            private readonly string _operationName;
            private readonly DateTime _startTime;

            public ExecutionTimeLogger(ILogger logger, string operationName)
            {
                _logger = logger;
                _operationName = operationName;
                _startTime = DateTime.Now;
                _logger.LogDebug("开始执行操作: {OperationName}", _operationName);
            }

            public void Dispose()
            {
                var elapsed = DateTime.Now - _startTime;
                _logger.LogInformation("操作完成: {OperationName}，耗时: {ElapsedMs}ms",
                    _operationName, elapsed.TotalMilliseconds);
            }
        }
    }
}

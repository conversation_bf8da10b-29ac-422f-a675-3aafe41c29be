using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace TeklaList.Services.Caching
{
    /// <summary>
    /// 内存缓存服务实现
    /// </summary>
    public class MemoryCacheService : ICacheService, IDisposable
    {
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly ConcurrentDictionary<string, CacheItem> _cache;
        private readonly Timer _cleanupTimer;
        private readonly object _lockObject = new object();
        
        private int _maxItems = 1000;
        private long _hitCount;
        private long _missCount;
        private bool _disposed;

        public MemoryCacheService(ILogger<MemoryCacheService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cache = new ConcurrentDictionary<string, CacheItem>();
            
            // 每5分钟清理一次过期项
            _cleanupTimer = new Timer(CleanupCallback, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
            
            _logger.LogInformation("内存缓存服务已初始化");
        }

        public T? Get<T>(string key) where T : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));

            if (_cache.TryGetValue(key, out var item))
            {
                if (item.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    Interlocked.Increment(ref _missCount);
                    return null;
                }

                item.LastAccessed = DateTime.UtcNow;
                Interlocked.Increment(ref _hitCount);
                return item.Value as T;
            }

            Interlocked.Increment(ref _missCount);
            return null;
        }

        public Task<T?> GetAsync<T>(string key) where T : class
        {
            return Task.FromResult(Get<T>(key));
        }

        public void Set<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("缓存键不能为空", nameof(key));

            if (value == null)
                throw new ArgumentNullException(nameof(value));

            var expirationTime = expiration.HasValue 
                ? DateTime.UtcNow.Add(expiration.Value) 
                : DateTime.MaxValue;

            var cacheItem = new CacheItem
            {
                Key = key,
                Value = value,
                ExpirationTime = expirationTime,
                CreatedTime = DateTime.UtcNow,
                LastAccessed = DateTime.UtcNow
            };

            _cache.AddOrUpdate(key, cacheItem, (k, existing) => cacheItem);

            // 检查缓存大小限制
            EnforceSizeLimit();

            _logger.LogDebug("缓存项已设置: {Key}, 过期时间: {ExpirationTime}", key, expirationTime);
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            Set(key, value, expiration);
            return Task.CompletedTask;
        }

        public T GetOrCreate<T>(string key, Func<T> factory, TimeSpan? expiration = null) where T : class
        {
            var cached = Get<T>(key);
            if (cached != null)
                return cached;

            var value = factory();
            Set(key, value, expiration);
            return value;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null) where T : class
        {
            var cached = await GetAsync<T>(key);
            if (cached != null)
                return cached;

            var value = await factory();
            await SetAsync(key, value, expiration);
            return value;
        }

        public bool Remove(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            var removed = _cache.TryRemove(key, out _);
            if (removed)
            {
                _logger.LogDebug("缓存项已移除: {Key}", key);
            }
            return removed;
        }

        public Task<bool> RemoveAsync(string key)
        {
            return Task.FromResult(Remove(key));
        }

        public void Clear()
        {
            var count = _cache.Count;
            _cache.Clear();
            _logger.LogInformation("已清空所有缓存项，共 {Count} 项", count);
        }

        public Task ClearAsync()
        {
            Clear();
            return Task.CompletedTask;
        }

        public bool Exists(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            if (_cache.TryGetValue(key, out var item))
            {
                if (item.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    return false;
                }
                return true;
            }
            return false;
        }

        public Task<bool> ExistsAsync(string key)
        {
            return Task.FromResult(Exists(key));
        }

        public IEnumerable<string> GetKeys()
        {
            return _cache.Keys.ToList();
        }

        public CacheStatistics GetStatistics()
        {
            var expiredCount = _cache.Values.Count(item => item.IsExpired);
            
            return new CacheStatistics
            {
                TotalItems = _cache.Count,
                HitCount = _hitCount,
                MissCount = _missCount,
                ExpiredItems = expiredCount,
                EstimatedMemoryUsage = EstimateMemoryUsage(),
                LastCleanupTime = _lastCleanupTime
            };
        }

        public void SetSizeLimit(int maxItems)
        {
            if (maxItems <= 0)
                throw new ArgumentException("最大项数必须大于0", nameof(maxItems));

            _maxItems = maxItems;
            EnforceSizeLimit();
            _logger.LogInformation("缓存大小限制已设置为: {MaxItems}", maxItems);
        }

        public int CleanupExpired()
        {
            var expiredKeys = _cache
                .Where(kvp => kvp.Value.IsExpired)
                .Select(kvp => kvp.Key)
                .ToList();

            var removedCount = 0;
            foreach (var key in expiredKeys)
            {
                if (_cache.TryRemove(key, out _))
                    removedCount++;
            }

            if (removedCount > 0)
            {
                _logger.LogDebug("已清理 {Count} 个过期缓存项", removedCount);
            }

            _lastCleanupTime = DateTime.UtcNow;
            return removedCount;
        }

        private void EnforceSizeLimit()
        {
            if (_cache.Count <= _maxItems) return;

            lock (_lockObject)
            {
                if (_cache.Count <= _maxItems) return;

                // 移除最旧的项目
                var itemsToRemove = _cache.Count - _maxItems;
                var oldestItems = _cache.Values
                    .OrderBy(item => item.LastAccessed)
                    .Take(itemsToRemove)
                    .ToList();

                foreach (var item in oldestItems)
                {
                    _cache.TryRemove(item.Key, out _);
                }

                _logger.LogDebug("已移除 {Count} 个最旧的缓存项以满足大小限制", itemsToRemove);
            }
        }

        private long EstimateMemoryUsage()
        {
            // 简单的内存使用估算
            return _cache.Count * 1024; // 假设每个项目平均1KB
        }

        private DateTime? _lastCleanupTime;

        private void CleanupCallback(object? state)
        {
            try
            {
                CleanupExpired();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期缓存项时发生错误");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _cleanupTimer?.Dispose();
            _cache.Clear();
            _disposed = true;
            
            _logger.LogInformation("内存缓存服务已释放");
        }

        private class CacheItem
        {
            public string Key { get; set; } = string.Empty;
            public object Value { get; set; } = null!;
            public DateTime ExpirationTime { get; set; }
            public DateTime CreatedTime { get; set; }
            public DateTime LastAccessed { get; set; }

            public bool IsExpired => DateTime.UtcNow > ExpirationTime;
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Collections;
#if TEKLA_AVAILABLE
using Tekla.Structures.Model;
using Tekla.Structures.Model.UI;
using Tekla.Structures.Filtering;
using Tekla.Structures.Filtering.Categories;
#endif
using TeklaList.Models;
using TeklaList.Utils;

namespace TeklaList.Services
{
    public class TeklaModelService
    {
#if TEKLA_AVAILABLE
        private readonly Model _model;
#endif
        private readonly Dictionary<string, List<TeklaModelPart>> _assemblyCache;
        private bool _useMockData;
        private List<TeklaModelPart> _mockParts;

        // 缓存相关字段
        private List<TeklaModelPart> _partsCache;
        private DateTime _partsCacheTimestamp = DateTime.MinValue;
        private readonly TimeSpan _cacheDuration = TimeSpan.FromMinutes(5); // 缓存有效期5分钟

        private Dictionary<string, List<int>> _assemblyPartIdsCache = new Dictionary<string, List<int>>();

        public TeklaModelService()
        {
            try
            {
#if TEKLA_AVAILABLE
                _model = new Model();
#endif
                _assemblyCache = new Dictionary<string, List<TeklaModelPart>>();
                _useMockData = false;
                _mockParts = new List<TeklaModelPart>();
                _assemblyPartIdsCache = new Dictionary<string, List<int>>();

#if TEKLA_AVAILABLE
                // 尝试初始化连接，如果失败则记录错误
                if (!_model.GetConnectionStatus())
                {
                    LogError("无法连接到Tekla模型，请确保Tekla Structures已启动并打开了模型");
                }
#else
                _useMockData = true;
                LogInfo("Tekla Structures未安装，使用模拟数据模式");
#endif
            }
            catch (Exception ex)
            {
                LogError($"初始化Tekla模型服务时发生错误: {ex.Message}");
                _useMockData = true;
                InitializeMockData();
            }
        }

        // Mock data methods
        private void InitializeMockData()
        {
            _mockParts = new List<TeklaModelPart>
            {
                new TeklaModelPart(1, "BEAM", "P1", "HEA200", "S355", "", "1", "1", "", 0, "A1", "", "", "", "", "", 1),
                new TeklaModelPart(2, "COLUMN", "P2", "HEB300", "S355", "", "1", "1", "", 0, "A2", "", "", "", "", "", 2)
            };
        }

        public async Task<List<TeklaModelPart>> GetPartsAsync(CancellationToken cancellationToken = default)
        {
            if (_useMockData)
            {
                await Task.Delay(500, cancellationToken); // 模拟网络延迟
                return new List<TeklaModelPart>(_mockParts);
            }

#if TEKLA_AVAILABLE
            return await Task.Run(() => GetPartsFromModel(), cancellationToken);
#else
            return new List<TeklaModelPart>(_mockParts);
#endif
        }

#if TEKLA_AVAILABLE
        private List<TeklaModelPart> GetPartsFromModel()
        {
            var parts = new List<TeklaModelPart>();

            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("模型连接已断开");
                    return parts;
                }

                var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();

                while (modelObjectEnumerator.MoveNext())
                {
                    if (modelObjectEnumerator.Current is Part part)
                    {
                        var teklaModelPart = ConvertToTeklaModelPart(part);
                        if (teklaModelPart != null)
                        {
                            parts.Add(teklaModelPart);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"获取构件列表时发生错误: {ex.Message}");
            }

            return parts;
        }

        private TeklaModelPart ConvertToTeklaModelPart(Part part)
        {
            try
            {
                // 获取构件属性
                part.GetReportProperty("NAME", ref var name);
                part.GetReportProperty("PROFILE", ref var profile);
                part.GetReportProperty("MATERIAL", ref var material);
                part.GetReportProperty("LENGTH", ref var length);
                part.GetReportProperty("WEIGHT", ref var weight);
                part.GetReportProperty("ASSEMBLY_POS", ref var assemblyNumber);
                part.GetReportProperty("PART_POS", ref var partNumber);
                part.GetReportProperty("PHASE", ref var phase);

                var teklaModelPart = new TeklaModelPart(
                    0, // index
                    name?.ToString() ?? "",
                    partNumber?.ToString() ?? "",
                    profile?.ToString() ?? "",
                    material?.ToString() ?? "",
                    "", // finish
                    "", // class
                    phase?.ToString() ?? "",
                    "", // boughtItem
                    0, // boltCount
                    assemblyNumber?.ToString() ?? "",
                    "", // assemblyPrefix
                    "", // assemblyStartNumber
                    "", // partPrefix
                    "", // partStartNumber
                    part.Identifier.GUID.ToString(),
                    part.Identifier.ID
                );

                return teklaModelPart;
            }
            catch (Exception ex)
            {
                LogError($"转换构件数据时发生错误: {ex.Message}");
                return null;
            }
        }
#endif

        public async Task<List<TeklaModelPart>> GetAssemblyPartsAsync(string assemblyNumber, CancellationToken cancellationToken = default)
        {
            if (_useMockData)
            {
                await Task.Delay(300, cancellationToken);
                return _mockParts.Where(p => p.AssemblyNumber == assemblyNumber).ToList();
            }

#if TEKLA_AVAILABLE
            return await Task.Run(() => GetAssemblyPartsFromModel(assemblyNumber), cancellationToken);
#else
            return _mockParts.Where(p => p.AssemblyNumber == assemblyNumber).ToList();
#endif
        }

#if TEKLA_AVAILABLE
        private List<TeklaModelPart> GetAssemblyPartsFromModel(string assemblyNumber)
        {
            var parts = new List<TeklaModelPart>();

            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("模型连接已断开");
                    return parts;
                }

                var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();

                while (modelObjectEnumerator.MoveNext())
                {
                    if (modelObjectEnumerator.Current is Part part)
                    {
                        part.GetReportProperty("ASSEMBLY_POS", ref var assemblyPos);
                        if (assemblyPos?.ToString() == assemblyNumber)
                        {
                            var teklaModelPart = ConvertToTeklaModelPart(part);
                            if (teklaModelPart != null)
                            {
                                parts.Add(teklaModelPart);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"获取装配体构件列表时发生错误: {ex.Message}");
            }

            return parts;
        }
#endif

        public bool IsConnected()
        {
            if (_useMockData) return true;

#if TEKLA_AVAILABLE
            try
            {
                return _model?.GetConnectionStatus() ?? false;
            }
            catch
            {
                return false;
            }
#else
            return false;
#endif
        }

        public void HighlightParts(List<int> partIds)
        {
            if (_useMockData) return;

#if TEKLA_AVAILABLE
            try
            {
                if (!_model.GetConnectionStatus()) return;

                var selector = new Tekla.Structures.Model.UI.ModelObjectSelector();
                var identifiers = partIds.Select(id => new Identifier(id)).ToList();

                selector.Select(identifiers);
            }
            catch (Exception ex)
            {
                LogError($"高亮显示构件时发生错误: {ex.Message}");
            }
#endif
        }

        // 为向后兼容添加的方法
        public bool GetConnectionStatus()
        {
            return IsConnected();
        }

        public async Task<List<TeklaModelPart>> GetAllParts()
        {
            return await GetPartsAsync();
        }

        public async Task<List<TeklaModelPart>> GetSelectedParts()
        {
            // 暂时返回空列表，实际实现需要Tekla API
            return new List<TeklaModelPart>();
        }

        public void ClearCache()
        {
            _assemblyCache?.Clear();
            _partsCache?.Clear();
            _assemblyPartIdsCache?.Clear();
        }

        public void HighlightObjects(List<TeklaModelPart> parts)
        {
            var partIds = parts.Where(p => p.ModelObjectId.HasValue).Select(p => p.ModelObjectId.Value).ToList();
            HighlightParts(partIds);
        }

        public async Task<List<AssemblyInfo>> GetAssemblies(List<TeklaModelPart> parts)
        {
            // 简单的构件分组逻辑
            var assemblies = parts.GroupBy(p => p.AssemblyNumber)
                                 .Where(g => !string.IsNullOrWhiteSpace(g.Key))
                                 .Select(g => new AssemblyInfo
                                 {
                                     AssemblyNumber = g.Key,
                                     Parts = g.ToList()
                                 })
                                 .ToList();
            return assemblies;
        }

        public void HighlightAssembliesUsingCache(List<AssemblyInfo> assemblies)
        {
            var allParts = assemblies.SelectMany(a => a.Parts).ToList();
            HighlightObjects(allParts);
        }

        public List<int> GetPartIdsForAssembly(string assemblyNumber)
        {
            return _mockParts.Where(p => p.AssemblyNumber == assemblyNumber)
                           .Where(p => p.ModelObjectId.HasValue)
                           .Select(p => p.ModelObjectId.Value)
                           .ToList();
        }

        public void LogInfo(string message)
        {
            Logger.LogInfo(message);
        }

        public void LogError(string message)
        {
            Logger.LogError(message);
        }

        public void Dispose()
        {
            _assemblyCache?.Clear();
            _partsCache?.Clear();
            _assemblyPartIdsCache?.Clear();
        }
    }
}


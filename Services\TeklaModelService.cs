using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Collections;
#if TEKLA_AVAILABLE
using Tekla.Structures.Model;
using Tekla.Structures.Model.UI;
using Tekla.Structures.Filtering;
using Tekla.Structures.Filtering.Categories;
#endif
using TeklaList.Models;
using TeklaList.Utils;

namespace TeklaList.Services
{
    public class TeklaModelService
    {
#if TEKLA_AVAILABLE
        private readonly Model _model;
#endif
        private readonly Dictionary<string, List<TeklaModelPart>> _assemblyCache;
        private bool _useMockData;
        private List<TeklaModelPart> _mockParts;

        // 缓存相关字段
        private List<TeklaModelPart> _partsCache;
        private DateTime _partsCacheTimestamp = DateTime.MinValue;
        private readonly TimeSpan _cacheDuration = TimeSpan.FromMinutes(5); // 缓存有效期5分钟

        private Dictionary<string, List<int>> _assemblyPartIdsCache = new Dictionary<string, List<int>>();

        public TeklaModelService()
        {
            try
            {
#if TEKLA_AVAILABLE
                _model = new Model();
#endif
                _assemblyCache = new Dictionary<string, List<TeklaModelPart>>();
                _useMockData = false;
                _mockParts = new List<TeklaModelPart>();
                _assemblyPartIdsCache = new Dictionary<string, List<int>>();

#if TEKLA_AVAILABLE
                // 尝试初始化连接，如果失败则记录错误
                if (!_model.GetConnectionStatus())
                {
                    LogError("无法连接到Tekla模型，请确保Tekla Structures已启动并打开了模型");
                }
#else
                _useMockData = true;
                LogInfo("Tekla Structures未安装，使用模拟数据模式");
#endif
            }
            catch (Exception ex)
            {
                LogError($"初始化Tekla模型服务时发生错误: {ex.Message}");
                _useMockData = true;
                InitializeMockData();
            }
        }

        // Mock data methods
        private void InitializeMockData()
        {
            _mockParts = new List<TeklaModelPart>
            {
                new TeklaModelPart
                {
                    Id = 1,
                    Name = "BEAM",
                    Profile = "HEA200",
                    Material = "S355",
                    Length = 5000,
                    Weight = 157.0,
                    AssemblyNumber = "A1",
                    PartNumber = "P1",
                    Phase = 1
                },
                new TeklaModelPart
                {
                    Id = 2,
                    Name = "COLUMN",
                    Profile = "HEB300",
                    Material = "S355",
                    Length = 3000,
                    Weight = 262.0,
                    AssemblyNumber = "A2",
                    PartNumber = "P2",
                    Phase = 1
                }
            };
        }

        public async Task<List<TeklaModelPart>> GetPartsAsync(CancellationToken cancellationToken = default)
        {
            if (_useMockData)
            {
                await Task.Delay(500, cancellationToken); // 模拟网络延迟
                return new List<TeklaModelPart>(_mockParts);
            }

#if TEKLA_AVAILABLE
            return await Task.Run(() => GetPartsFromModel(), cancellationToken);
#else
            return new List<TeklaModelPart>(_mockParts);
#endif
        }

#if TEKLA_AVAILABLE
        private List<TeklaModelPart> GetPartsFromModel()
        {
            var parts = new List<TeklaModelPart>();
            
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("模型连接已断开");
                    return parts;
                }

                var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();
                
                while (modelObjectEnumerator.MoveNext())
                {
                    if (modelObjectEnumerator.Current is Part part)
                    {
                        var teklaModelPart = ConvertToTeklaModelPart(part);
                        if (teklaModelPart != null)
                        {
                            parts.Add(teklaModelPart);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"获取构件列表时发生错误: {ex.Message}");
            }

            return parts;
        }

        private TeklaModelPart ConvertToTeklaModelPart(Part part)
        {
            try
            {
                var teklaModelPart = new TeklaModelPart
                {
                    Id = part.Identifier.ID
                };

                // 获取构件属性
                part.GetReportProperty("NAME", ref var name);
                part.GetReportProperty("PROFILE", ref var profile);
                part.GetReportProperty("MATERIAL", ref var material);
                part.GetReportProperty("LENGTH", ref var length);
                part.GetReportProperty("WEIGHT", ref var weight);
                part.GetReportProperty("ASSEMBLY_POS", ref var assemblyNumber);
                part.GetReportProperty("PART_POS", ref var partNumber);
                part.GetReportProperty("PHASE", ref var phase);

                teklaModelPart.Name = name?.ToString() ?? "";
                teklaModelPart.Profile = profile?.ToString() ?? "";
                teklaModelPart.Material = material?.ToString() ?? "";
                teklaModelPart.AssemblyNumber = assemblyNumber?.ToString() ?? "";
                teklaModelPart.PartNumber = partNumber?.ToString() ?? "";

                if (double.TryParse(length?.ToString(), out var lengthValue))
                    teklaModelPart.Length = lengthValue;

                if (double.TryParse(weight?.ToString(), out var weightValue))
                    teklaModelPart.Weight = weightValue;

                if (int.TryParse(phase?.ToString(), out var phaseValue))
                    teklaModelPart.Phase = phaseValue;

                return teklaModelPart;
            }
            catch (Exception ex)
            {
                LogError($"转换构件数据时发生错误: {ex.Message}");
                return null;
            }
        }
#endif

        public async Task<List<TeklaModelPart>> GetAssemblyPartsAsync(string assemblyNumber, CancellationToken cancellationToken = default)
        {
            if (_useMockData)
            {
                await Task.Delay(300, cancellationToken);
                return _mockParts.Where(p => p.AssemblyNumber == assemblyNumber).ToList();
            }

#if TEKLA_AVAILABLE
            return await Task.Run(() => GetAssemblyPartsFromModel(assemblyNumber), cancellationToken);
#else
            return _mockParts.Where(p => p.AssemblyNumber == assemblyNumber).ToList();
#endif
        }

#if TEKLA_AVAILABLE
        private List<TeklaModelPart> GetAssemblyPartsFromModel(string assemblyNumber)
        {
            var parts = new List<TeklaModelPart>();
            
            try
            {
                if (!_model.GetConnectionStatus())
                {
                    LogError("模型连接已断开");
                    return parts;
                }

                var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();
                
                while (modelObjectEnumerator.MoveNext())
                {
                    if (modelObjectEnumerator.Current is Part part)
                    {
                        part.GetReportProperty("ASSEMBLY_POS", ref var assemblyPos);
                        if (assemblyPos?.ToString() == assemblyNumber)
                        {
                            var teklaModelPart = ConvertToTeklaModelPart(part);
                            if (teklaModelPart != null)
                            {
                                parts.Add(teklaModelPart);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"获取装配体构件列表时发生错误: {ex.Message}");
            }

            return parts;
        }
#endif

        public bool IsConnected()
        {
            if (_useMockData) return true;
            
#if TEKLA_AVAILABLE
            try
            {
                return _model?.GetConnectionStatus() ?? false;
            }
            catch
            {
                return false;
            }
#else
            return false;
#endif
        }

        public void HighlightParts(List<int> partIds)
        {
            if (_useMockData) return;
            
#if TEKLA_AVAILABLE
            try
            {
                if (!_model.GetConnectionStatus()) return;

                var selector = new Tekla.Structures.Model.UI.ModelObjectSelector();
                var identifiers = partIds.Select(id => new Identifier(id)).ToList();
                
                selector.Select(identifiers);
            }
            catch (Exception ex)
            {
                LogError($"高亮显示构件时发生错误: {ex.Message}");
            }
#endif
        }

        private void LogError(string message)
        {
            Logger.LogError(message);
        }

        private void LogInfo(string message)
        {
            Logger.LogInfo(message);
        }

        public void Dispose()
        {
            _assemblyCache?.Clear();
            _partsCache?.Clear();
            _assemblyPartIdsCache?.Clear();
        }
    }
}


using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace TeklaList.Configuration
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public class ConfigurationManager
    {
        private readonly ILogger<ConfigurationManager> _logger;
        private readonly string _configDirectory;
        private readonly string _configFilePath;
        private AppSettings? _settings;

        public ConfigurationManager(ILogger<ConfigurationManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // 配置文件存储在用户的AppData目录
            _configDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "TeklaList");
            
            _configFilePath = Path.Combine(_configDirectory, "Settings.json");
            
            // 确保配置目录存在
            Directory.CreateDirectory(_configDirectory);
        }

        /// <summary>
        /// 获取应用程序设置
        /// </summary>
        public AppSettings Settings => _settings ??= LoadSettings();

        /// <summary>
        /// 加载设置
        /// </summary>
        private AppSettings LoadSettings()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var settings = JsonConvert.DeserializeObject<AppSettings>(json);
                    if (settings != null)
                    {
                        _logger.LogInformation("配置文件加载成功: {ConfigPath}", _configFilePath);
                        return settings;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置文件失败: {ConfigPath}", _configFilePath);
            }

            // 如果加载失败或文件不存在，返回默认设置
            _logger.LogInformation("使用默认配置设置");
            return new AppSettings();
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        public async Task SaveSettingsAsync()
        {
            try
            {
                if (_settings == null) return;

                var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                await File.WriteAllTextAsync(_configFilePath, json);
                
                _logger.LogInformation("配置文件保存成功: {ConfigPath}", _configFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置文件失败: {ConfigPath}", _configFilePath);
                throw;
            }
        }

        /// <summary>
        /// 同步保存设置
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                if (_settings == null) return;

                var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
                
                _logger.LogInformation("配置文件保存成功: {ConfigPath}", _configFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置文件失败: {ConfigPath}", _configFilePath);
                throw;
            }
        }

        /// <summary>
        /// 重置设置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            _logger.LogInformation("重置配置为默认值");
            _settings = new AppSettings();
        }

        /// <summary>
        /// 获取配置目录路径
        /// </summary>
        public string GetConfigDirectory() => _configDirectory;

        /// <summary>
        /// 获取日志目录路径
        /// </summary>
        public string GetLogDirectory() => Path.Combine(_configDirectory, "Logs");

        /// <summary>
        /// 获取缓存目录路径
        /// </summary>
        public string GetCacheDirectory() => Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "TeklaList", "Cache");
    }
}

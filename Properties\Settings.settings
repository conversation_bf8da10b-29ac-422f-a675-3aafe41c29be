<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="TeklaList.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="WindowLeft" Type="System.Double" Scope="User">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="WindowTop" Type="System.Double" Scope="User">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1200</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">800</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.Windows.WindowState" Scope="User">
      <Value Profile="(Default)">Normal</Value>
    </Setting>
    <Setting Name="IsAssemblyMode" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="AutoRefreshInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">30</Value>
    </Setting>
    <Setting Name="CacheExpirationMinutes" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">10</Value>
    </Setting>
    <Setting Name="MaxCacheSize" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1000</Value>
    </Setting>
    <Setting Name="EnableLogging" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="LogLevel" Type="System.String" Scope="User">
      <Value Profile="(Default)">Info</Value>
    </Setting>
    <Setting Name="LogRetentionDays" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">7</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">zh-CN</Value>
    </Setting>
    <Setting Name="ShowStatusBar" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowToolbar" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AutoSaveSettings" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="CheckForUpdates" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TeklaConnectionTimeout" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5000</Value>
    </Setting>
    <Setting Name="EnableHighlighting" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="HighlightColor" Type="System.String" Scope="User">
      <Value Profile="(Default)">#FF0000</Value>
    </Setting>
    <Setting Name="FilterCaseSensitive" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="FilterWholeWord" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="GridFontSize" Type="System.Double" Scope="User">
      <Value Profile="(Default)">12</Value>
    </Setting>
    <Setting Name="GridRowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">25</Value>
    </Setting>
    <Setting Name="ShowGridLines" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AlternatingRowColors" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="EnableVirtualization" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="MaxLoadItems" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">10000</Value>
    </Setting>
    <Setting Name="LoadTimeout" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">30000</Value>
    </Setting>
    <Setting Name="EnableProgressBar" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowLoadingAnimation" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="EnableSounds" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="EnableNotifications" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AutoBackup" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BackupInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">60</Value>
    </Setting>
    <Setting Name="MaxBackupFiles" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5</Value>
    </Setting>
  </Settings>
</SettingsFile>
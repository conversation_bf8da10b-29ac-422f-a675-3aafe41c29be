using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TeklaList.Models;
using TeklaList.Services.Caching;
using TeklaList.Services.Logging;

#if TEKLA_AVAILABLE
using Tekla.Structures.Model;
using Tekla.Structures.Model.UI;
using Tekla.Structures.Geometry3d;
using Tekla.Structures.Filtering;
#endif

namespace TeklaList.Services
{
    /// <summary>
    /// 高性能Tekla模型服务实现
    /// </summary>
    public class HighPerformanceTeklaModelService : ITeklaModelService
    {
        private readonly ILogger<HighPerformanceTeklaModelService> _logger;
        private readonly ICacheService _cacheService;
        private readonly SemaphoreSlim _semaphore;
        private readonly CancellationTokenSource _disposeCts;

#if TEKLA_AVAILABLE
        private readonly Model _model;
        private readonly ModelObjectSelector _selector;
#endif

        private bool _isConnected;
        private bool _disposed;
        private readonly Timer _connectionCheckTimer;

        // 缓存键常量
        private const string ALL_PARTS_CACHE_KEY = "all_parts";
        private const string SELECTED_PARTS_CACHE_KEY = "selected_parts";
        private const string ASSEMBLIES_CACHE_KEY = "assemblies";
        private const string MODEL_STATS_CACHE_KEY = "model_statistics";

        public event EventHandler<bool>? ConnectionStatusChanged;
        public event EventHandler? ModelChanged;

        public bool IsConnected
        {
            get => _isConnected;
            private set
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    ConnectionStatusChanged?.Invoke(this, value);
                    _logger.LogInformation("Tekla连接状态变更: {Status}", value ? "已连接" : "已断开");
                }
            }
        }

        public HighPerformanceTeklaModelService(
            ILogger<HighPerformanceTeklaModelService> logger,
            ICacheService cacheService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));

            _semaphore = new SemaphoreSlim(4, 4); // 最多4个并发操作
            _disposeCts = new CancellationTokenSource();

#if TEKLA_AVAILABLE
            try
            {
                _model = new Model();
                _selector = new ModelObjectSelector();
                IsConnected = _model.GetConnectionStatus();

                _logger.LogInformation("Tekla模型服务初始化成功，连接状态: {Status}", IsConnected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化Tekla模型服务失败");
                IsConnected = false;
            }
#else
            _logger.LogWarning("Tekla Structures未安装，使用模拟模式");
            IsConnected = false;
#endif

            // 每30秒检查一次连接状态
            _connectionCheckTimer = new Timer(CheckConnectionStatus, null,
                TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        public async Task<List<TeklaModelPart>> GetAllPartsAsync(CancellationToken cancellationToken = default)
        {
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _disposeCts.Token);

            return await _cacheService.GetOrCreateAsync(
                ALL_PARTS_CACHE_KEY,
                async () => await GetPartsFromModelAsync(combinedCts.Token),
                TimeSpan.FromMinutes(5));
        }

        public async Task<List<TeklaModelPart>> GetSelectedPartsAsync(CancellationToken cancellationToken = default)
        {
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _disposeCts.Token);

            // 选中的零件不缓存，因为选择状态经常变化
            return await GetSelectedPartsFromModelAsync(combinedCts.Token);
        }

        public async Task<List<TeklaModelPart>> GetAssemblyPartsAsync(string assemblyNumber, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(assemblyNumber))
                return new List<TeklaModelPart>();

            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _disposeCts.Token);

            var cacheKey = $"assembly_parts_{assemblyNumber}";
            return await _cacheService.GetOrCreateAsync(
                cacheKey,
                async () => await GetAssemblyPartsFromModelAsync(assemblyNumber, combinedCts.Token),
                TimeSpan.FromMinutes(10));
        }

        public async Task<List<AssemblyInfo>> GetAssembliesAsync(List<TeklaModelPart>? parts = null, CancellationToken cancellationToken = default)
        {
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, _disposeCts.Token);

            if (parts == null)
            {
                return await _cacheService.GetOrCreateAsync(
                    ASSEMBLIES_CACHE_KEY,
                    async () => await BuildAssembliesFromAllPartsAsync(combinedCts.Token),
                    TimeSpan.FromMinutes(5));
            }

            return await BuildAssembliesFromPartsAsync(parts, combinedCts.Token);
        }

        public async Task HighlightPartsAsync(IEnumerable<TeklaModelPart> parts, bool clearPrevious = true, CancellationToken cancellationToken = default)
        {
            var partIds = parts?.Where(p => p.ModelObjectId.HasValue)
                              .Select(p => p.ModelObjectId!.Value)
                              .ToList() ?? new List<int>();

            await HighlightPartsByIdAsync(partIds, clearPrevious, cancellationToken);
        }

        public async Task HighlightPartsByIdAsync(IEnumerable<int> partIds, bool clearPrevious = true, CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
#if TEKLA_AVAILABLE
                await Task.Run(() =>
                {
                    if (clearPrevious)
                    {
                        _selector.GetAllObjects().Clear();
                    }

                    var identifiers = partIds.Select(id => new Identifier(id)).ToArray();
                    if (identifiers.Length > 0)
                    {
                        _selector.Select(identifiers);
                        _logger.LogDebug("已高亮 {Count} 个零件", identifiers.Length);
                    }
                }, cancellationToken);
#endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "高亮零件时发生错误");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task ClearHighlightAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
#if TEKLA_AVAILABLE
                await Task.Run(() =>
                {
                    _selector.GetAllObjects().Clear();
                    _logger.LogDebug("已清除所有高亮");
                }, cancellationToken);
#endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除高亮时发生错误");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task SelectPartsAsync(IEnumerable<TeklaModelPart> parts, bool clearPrevious = true, CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            var partIds = parts?.Where(p => p.ModelObjectId.HasValue)
                              .Select(p => p.ModelObjectId!.Value)
                              .ToList() ?? new List<int>();

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
#if TEKLA_AVAILABLE
                await Task.Run(() =>
                {
                    if (clearPrevious)
                    {
                        _selector.GetAllObjects().Clear();
                    }

                    var identifiers = partIds.Select(id => new Identifier(id)).ToArray();
                    if (identifiers.Length > 0)
                    {
                        _selector.Select(identifiers);
                        _logger.LogDebug("已选择 {Count} 个零件", identifiers.Length);
                    }
                }, cancellationToken);
#endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择零件时发生错误");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task ClearSelectionAsync(CancellationToken cancellationToken = default)
        {
            await ClearHighlightAsync(cancellationToken);
        }

        public async Task RefreshModelAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("开始刷新模型数据...");

            ClearCache();

#if TEKLA_AVAILABLE
            if (IsConnected)
            {
                await Task.Run(() =>
                {
                    _model.CommitChanges();
                }, cancellationToken);
            }
#endif

            ModelChanged?.Invoke(this, EventArgs.Empty);
            _logger.LogInformation("模型数据刷新完成");
        }

        public void ClearCache()
        {
            _cacheService.Clear();
            _logger.LogDebug("已清除所有缓存");
        }

        public async Task<ModelStatistics> GetModelStatisticsAsync(CancellationToken cancellationToken = default)
        {
            return await _cacheService.GetOrCreateAsync(
                MODEL_STATS_CACHE_KEY,
                async () => await CalculateModelStatisticsAsync(cancellationToken),
                TimeSpan.FromMinutes(10));
        }

        private void CheckConnectionStatus(object? state)
        {
            try
            {
#if TEKLA_AVAILABLE
                var newStatus = _model?.GetConnectionStatus() ?? false;
                IsConnected = newStatus;
#else
                IsConnected = false;
#endif
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查连接状态时发生错误");
                IsConnected = false;
            }
        }

        #region 私有方法

        private async Task<List<TeklaModelPart>> GetPartsFromModelAsync(CancellationToken cancellationToken)
        {
            if (!IsConnected) return new List<TeklaModelPart>();

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                using var _ = _logger.LogExecutionTime("获取所有零件");

#if TEKLA_AVAILABLE
                return await Task.Run(() =>
                {
                    var parts = new ConcurrentBag<TeklaModelPart>();
                    var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();
                    var partsList = new List<Part>();

                    // 首先收集所有Part对象
                    while (modelObjectEnumerator.MoveNext())
                    {
                        if (modelObjectEnumerator.Current is Part part)
                        {
                            partsList.Add(part);
                        }
                    }

                    // 并行处理转换
                    Parallel.ForEach(partsList, new ParallelOptions
                    {
                        CancellationToken = cancellationToken,
                        MaxDegreeOfParallelism = Environment.ProcessorCount
                    }, part =>
                    {
                        var teklaModelPart = ConvertToTeklaModelPart(part);
                        if (teklaModelPart != null)
                        {
                            parts.Add(teklaModelPart);
                        }
                    });

                    var result = parts.ToList();
                    _logger.LogInformation("成功获取 {Count} 个零件", result.Count);
                    return result;
                }, cancellationToken);
#else
                return GetMockParts();
#endif
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<List<TeklaModelPart>> GetSelectedPartsFromModelAsync(CancellationToken cancellationToken)
        {
            if (!IsConnected) return new List<TeklaModelPart>();

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
#if TEKLA_AVAILABLE
                return await Task.Run(() =>
                {
                    var parts = new List<TeklaModelPart>();
                    var selectedObjects = _selector.GetSelectedObjects();

                    while (selectedObjects.MoveNext())
                    {
                        if (selectedObjects.Current is Part part)
                        {
                            var teklaModelPart = ConvertToTeklaModelPart(part);
                            if (teklaModelPart != null)
                            {
                                parts.Add(teklaModelPart);
                            }
                        }
                    }

                    _logger.LogDebug("获取到 {Count} 个选中零件", parts.Count);
                    return parts;
                }, cancellationToken);
#else
                return new List<TeklaModelPart>();
#endif
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<List<TeklaModelPart>> GetAssemblyPartsFromModelAsync(string assemblyNumber, CancellationToken cancellationToken)
        {
            if (!IsConnected) return new List<TeklaModelPart>();

            await _semaphore.WaitAsync(cancellationToken);
            try
            {
#if TEKLA_AVAILABLE
                return await Task.Run(() =>
                {
                    var parts = new List<TeklaModelPart>();
                    var modelObjectEnumerator = _model.GetModelObjectSelector().GetAllObjects();

                    while (modelObjectEnumerator.MoveNext())
                    {
                        if (modelObjectEnumerator.Current is Part part)
                        {
                            part.GetReportProperty("ASSEMBLY_POS", ref var assemblyPos);
                            if (string.Equals(assemblyPos?.ToString(), assemblyNumber, StringComparison.OrdinalIgnoreCase))
                            {
                                var teklaModelPart = ConvertToTeklaModelPart(part);
                                if (teklaModelPart != null)
                                {
                                    parts.Add(teklaModelPart);
                                }
                            }
                        }
                    }

                    _logger.LogDebug("构件 {AssemblyNumber} 包含 {Count} 个零件", assemblyNumber, parts.Count);
                    return parts;
                }, cancellationToken);
#else
                return GetMockParts().Where(p => p.AssemblyNumber == assemblyNumber).ToList();
#endif
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<List<AssemblyInfo>> BuildAssembliesFromAllPartsAsync(CancellationToken cancellationToken)
        {
            var allParts = await GetAllPartsAsync(cancellationToken);
            return await BuildAssembliesFromPartsAsync(allParts, cancellationToken);
        }

        private async Task<List<AssemblyInfo>> BuildAssembliesFromPartsAsync(List<TeklaModelPart> parts, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                var assemblies = new ConcurrentDictionary<string, AssemblyInfo>();

                Parallel.ForEach(parts, new ParallelOptions
                {
                    CancellationToken = cancellationToken,
                    MaxDegreeOfParallelism = Environment.ProcessorCount
                }, part =>
                {
                    if (!string.IsNullOrWhiteSpace(part.AssemblyNumber))
                    {
                        assemblies.AddOrUpdate(part.AssemblyNumber,
                            new AssemblyInfo(part.AssemblyNumber, new List<TeklaModelPart> { part }),
                            (key, existing) =>
                            {
                                lock (existing)
                                {
                                    existing.AddPart(part);
                                }
                                return existing;
                            });
                    }
                });

                var result = assemblies.Values.ToList();
                _logger.LogInformation("构建了 {Count} 个构件", result.Count);
                return result;
            }, cancellationToken);
        }

        private async Task<ModelStatistics> CalculateModelStatisticsAsync(CancellationToken cancellationToken)
        {
            var allParts = await GetAllPartsAsync(cancellationToken);

            return await Task.Run(() =>
            {
                var stats = new ModelStatistics
                {
                    TotalParts = allParts.Count,
                    TotalAssemblies = allParts.Select(p => p.AssemblyNumber).Distinct().Count(),
                    UniqueMaterials = allParts.Select(p => p.Material).Distinct().Count(),
                    UniqueProfiles = allParts.Select(p => p.Profile).Distinct().Count(),
                    LastUpdated = DateTime.Now
                };

                _logger.LogInformation("模型统计: {TotalParts} 个零件, {TotalAssemblies} 个构件",
                    stats.TotalParts, stats.TotalAssemblies);

                return stats;
            }, cancellationToken);
        }

#if TEKLA_AVAILABLE
        private TeklaModelPart? ConvertToTeklaModelPart(Part part)
        {
            try
            {
                var teklaModelPart = new TeklaModelPart(0); // 临时索引，稍后设置

                // 获取基本属性
                part.GetReportProperty("NAME", ref var name);
                part.GetReportProperty("PROFILE", ref var profile);
                part.GetReportProperty("MATERIAL", ref var material);
                part.GetReportProperty("ASSEMBLY_POS", ref var assemblyNumber);
                part.GetReportProperty("PART_POS", ref var partNumber);
                part.GetReportProperty("PHASE", ref var phase);
                part.GetReportProperty("CLASS", ref var classValue);
                part.GetReportProperty("FINISH", ref var finish);

                // 设置属性
                teklaModelPart.Name = name?.ToString() ?? string.Empty;
                teklaModelPart.Profile = profile?.ToString() ?? string.Empty;
                teklaModelPart.Material = material?.ToString() ?? string.Empty;
                teklaModelPart.AssemblyNumber = assemblyNumber?.ToString() ?? string.Empty;
                teklaModelPart.PartNumber = partNumber?.ToString() ?? string.Empty;
                teklaModelPart.Phase = phase?.ToString() ?? string.Empty;
                teklaModelPart.Class = classValue?.ToString() ?? string.Empty;
                teklaModelPart.Finish = finish?.ToString() ?? string.Empty;
                teklaModelPart.ModelObjectId = part.Identifier.ID;
                teklaModelPart.Guid = part.Identifier.GUID.ToString();

                return teklaModelPart;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "转换零件数据时发生错误，零件ID: {PartId}", part?.Identifier?.ID);
                return null;
            }
        }
#endif

        private List<TeklaModelPart> GetMockParts()
        {
            return new List<TeklaModelPart>
            {
                new TeklaModelPart(1, "BEAM", "P1", "HEA200", "S355", "", "1", "1", "", 0, "A1"),
                new TeklaModelPart(2, "COLUMN", "P2", "HEB300", "S355", "", "1", "1", "", 0, "A2"),
                new TeklaModelPart(3, "PLATE", "P3", "PL10", "S355", "", "1", "1", "", 0, "A1")
            };
        }

        #endregion

        public void Dispose()
        {
            if (_disposed) return;

            _disposeCts.Cancel();
            _connectionCheckTimer?.Dispose();
            _semaphore?.Dispose();
            _disposeCts?.Dispose();

            _disposed = true;
            _logger.LogInformation("Tekla模型服务已释放");
        }
    }
}

using System;
using System.IO;
#if TEKLA_AVAILABLE
using Tekla.Structures;
#endif

namespace TeklaList.Models
{
    public class ModelConnection
    {
#if TEKLA_AVAILABLE
        private Tekla.Structures.Model.Model _currentModel = new Tekla.Structures.Model.Model();

        public Tekla.Structures.Model.Model CurrentModel => _currentModel;
#endif

        public bool Connect()
        {
            try
            {
#if TEKLA_AVAILABLE
                return _currentModel.GetConnectionStatus();
#else
                return false;
#endif
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool IsConnected()
        {
#if TEKLA_AVAILABLE
            return _currentModel != null && _currentModel.GetConnectionStatus();
#else
            return false;
#endif
        }
    }
}

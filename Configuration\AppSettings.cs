using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TeklaList.Configuration
{
    /// <summary>
    /// 应用程序设置类
    /// </summary>
    public class AppSettings : INotifyPropertyChanged
    {
        private bool _isTopMost;
        private bool _enableHighlight = true;
        private bool _isMergeRows;
        private bool _isDarkTheme;
        private string _language = "zh-CN";
        private int _cacheSize = 1000;
        private int _maxConcurrentOperations = 4;
        private bool _enableVirtualization = true;
        private bool _enableAutoSave = true;
        private int _autoSaveInterval = 300; // 5分钟
        private bool _showDimensionLines;
        private string _highlightColor = "#FF4CAF50";
        private double _windowWidth = 1200;
        private double _windowHeight = 700;
        private double _windowLeft = 100;
        private double _windowTop = 100;
        private bool _windowMaximized;

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 窗口置顶
        /// </summary>
        public bool IsTopMost
        {
            get => _isTopMost;
            set => SetProperty(ref _isTopMost, value);
        }

        /// <summary>
        /// 启用高亮功能
        /// </summary>
        public bool EnableHighlight
        {
            get => _enableHighlight;
            set => SetProperty(ref _enableHighlight, value);
        }

        /// <summary>
        /// 合并相同行
        /// </summary>
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set => SetProperty(ref _isMergeRows, value);
        }

        /// <summary>
        /// 深色主题
        /// </summary>
        public bool IsDarkTheme
        {
            get => _isDarkTheme;
            set => SetProperty(ref _isDarkTheme, value);
        }

        /// <summary>
        /// 界面语言
        /// </summary>
        public string Language
        {
            get => _language;
            set => SetProperty(ref _language, value ?? "zh-CN");
        }

        /// <summary>
        /// 缓存大小
        /// </summary>
        public int CacheSize
        {
            get => _cacheSize;
            set => SetProperty(ref _cacheSize, Math.Max(100, value));
        }

        /// <summary>
        /// 最大并发操作数
        /// </summary>
        public int MaxConcurrentOperations
        {
            get => _maxConcurrentOperations;
            set => SetProperty(ref _maxConcurrentOperations, Math.Max(1, Math.Min(8, value)));
        }

        /// <summary>
        /// 启用虚拟化
        /// </summary>
        public bool EnableVirtualization
        {
            get => _enableVirtualization;
            set => SetProperty(ref _enableVirtualization, value);
        }

        /// <summary>
        /// 启用自动保存
        /// </summary>
        public bool EnableAutoSave
        {
            get => _enableAutoSave;
            set => SetProperty(ref _enableAutoSave, value);
        }

        /// <summary>
        /// 自动保存间隔（秒）
        /// </summary>
        public int AutoSaveInterval
        {
            get => _autoSaveInterval;
            set => SetProperty(ref _autoSaveInterval, Math.Max(60, value));
        }

        /// <summary>
        /// 显示尺寸线（高亮时）
        /// </summary>
        public bool ShowDimensionLines
        {
            get => _showDimensionLines;
            set => SetProperty(ref _showDimensionLines, value);
        }

        /// <summary>
        /// 高亮颜色
        /// </summary>
        public string HighlightColor
        {
            get => _highlightColor;
            set => SetProperty(ref _highlightColor, value ?? "#FF4CAF50");
        }

        /// <summary>
        /// 窗口宽度
        /// </summary>
        public double WindowWidth
        {
            get => _windowWidth;
            set => SetProperty(ref _windowWidth, Math.Max(800, value));
        }

        /// <summary>
        /// 窗口高度
        /// </summary>
        public double WindowHeight
        {
            get => _windowHeight;
            set => SetProperty(ref _windowHeight, Math.Max(600, value));
        }

        /// <summary>
        /// 窗口左边距
        /// </summary>
        public double WindowLeft
        {
            get => _windowLeft;
            set => SetProperty(ref _windowLeft, value);
        }

        /// <summary>
        /// 窗口上边距
        /// </summary>
        public double WindowTop
        {
            get => _windowTop;
            set => SetProperty(ref _windowTop, value);
        }

        /// <summary>
        /// 窗口最大化
        /// </summary>
        public bool WindowMaximized
        {
            get => _windowMaximized;
            set => SetProperty(ref _windowMaximized, value);
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}

<UserControl x:Class="TeklaList.Views.FilterableDataGrid"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:TeklaList.Views"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <DataGrid x:Name="MainDataGrid"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserResizeRows="False"
                  CanUserSortColumns="True"
                  SelectionMode="Extended"
                  SelectionUnit="FullRow"
                  GridLinesVisibility="All"
                  AlternatingRowBackground="AliceBlue"
                  RowHeaderWidth="30"
                  HeadersVisibility="All"
                  IsReadOnly="True">
        </DataGrid>
    </Grid>
</UserControl>

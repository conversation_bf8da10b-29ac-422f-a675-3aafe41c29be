using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using System.Threading;
using System.Threading.Tasks;
using TeklaList.Models;
using TeklaList.Services;
using TeklaList.Utils;

namespace TeklaList.ViewModels
{
    public class AssemblyListViewModel : ViewModelBase
    {
        private readonly TeklaModelService _teklaModelService;
        private readonly MainViewModel _mainViewModel;
        private readonly ObservableCollection<AssemblyInfo> _assemblies = new ObservableCollection<AssemblyInfo>();
        private readonly ObservableCollection<MergedAssemblyRow> _mergedAssemblies = new ObservableCollection<MergedAssemblyRow>();
        private bool _isMergeRows;

        // 标记是否正在进行高亮操作
        private bool _isHighlighting = false;

        // 缓存相关字段
        private Dictionary<string, List<int>> _assemblyNumberToModelObjectIdsCache = new Dictionary<string, List<int>>();
        private Dictionary<int, List<int>> _assemblyModelObjectIdToPartsCache = new Dictionary<int, List<int>>();

        // 高亮缓存 - 存储构件编号与其对应的所有零件ID
        private Dictionary<string, List<int>> _assemblyHighlightCache = new Dictionary<string, List<int>>();

        // 上一次高亮的构件编号缓存
        private List<string> _lastHighlightedAssemblyNumbers = new List<string>();
        private List<int> _lastHighlightedModelObjectIds = new List<int>();

        // 上一次高亮的零件ID列表
        private List<int> _lastHighlightedPartIds = new List<int>();

        public ObservableCollection<AssemblyInfo> Assemblies => _assemblies;
        public ObservableCollection<MergedAssemblyRow> MergedAssemblies => _mergedAssemblies;

        // 是否正在高亮
        public bool IsHighlighting
        {
            get => _isHighlighting;
            private set
            {
                if (SetProperty(ref _isHighlighting, value))
                {
                    // 通知UI更新
                    OnPropertyChanged();
                }
            }
        }
        public bool IsMergeRows
        {
            get => _isMergeRows;
            set
            {
                if (SetProperty(ref _isMergeRows, value))
                {
                    // 使用Task.Run在后台线程执行耗时操作
                    Task.Run(() =>
                    {
                        // 在UI线程上更新UI
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            // 更新合并行
                            UpdateMergedAssemblies();
                            // 通知视图更新
                            OnPropertyChanged(nameof(AssembliesView));
                        });
                    });
                }
            }
        }
        public IEnumerable<object> AssembliesView => IsMergeRows ? (IEnumerable<object>)MergedAssemblies : Assemblies;
        public AssemblyListViewModel(TeklaModelService service, MainViewModel mainViewModel = null)
        {
            _teklaModelService = service;
            _mainViewModel = mainViewModel;

            // 初始化缓存
            _assemblyNumberToModelObjectIdsCache = new Dictionary<string, List<int>>();
            _assemblyModelObjectIdToPartsCache = new Dictionary<int, List<int>>();
            _assemblyHighlightCache = new Dictionary<string, List<int>>();

            Assemblies.CollectionChanged += (s, e) =>
            {
                if (IsMergeRows) UpdateMergedAssemblies();

                // 当集合变化时，清空缓存
                if (e.Action == System.Collections.Specialized.NotifyCollectionChangedAction.Reset)
                {
                    _assemblyNumberToModelObjectIdsCache.Clear();
                    _assemblyModelObjectIdToPartsCache.Clear();
                    _assemblyHighlightCache.Clear();
                }
            };
        }
        public async void SetAssemblies(IEnumerable<AssemblyInfo> assemblies)
        {
            // 清空缓存
            _assemblyNumberToModelObjectIdsCache.Clear();
            _assemblyModelObjectIdToPartsCache.Clear();
            _assemblyHighlightCache.Clear();

            // 在后台线程处理数据
            var assemblyList = assemblies.ToList(); // 避免多次枚举

            // 清空当前集合
            Assemblies.Clear();

            // 使用批量添加，减少UI更新次数
            const int batchSize = 100; // 每批添加的数量

            for (int i = 0; i < assemblyList.Count; i += batchSize)
            {
                var batch = assemblyList.Skip(i).Take(batchSize).ToList();

                // 在UI线程添加批次数据
                foreach (var a in batch)
                {
                    Assemblies.Add(a);
                }

                // 如果不是最后一批，给UI线程一些时间响应
                if (i + batchSize < assemblyList.Count)
                {
                    await Task.Delay(10); // 短暂延迟，让UI有机会更新
                }
            }

            // 更新合并行
            if (IsMergeRows) UpdateMergedAssemblies();

            // 在后台预加载构件零件信息
            if (Assemblies.Count > 0)
            {
                TryGetAssemblyParts();
            }
        }

        private async void UpdateMergedAssemblies()
        {
            // 如果未开启合并行，直接返回
            if (!IsMergeRows) return;

            // 如果没有构件数据，直接返回
            if (Assemblies == null || Assemblies.Count == 0)
            {
                MergedAssemblies.Clear();
                OnPropertyChanged(nameof(AssembliesView));
                return;
            }

            try
            {
                // 在后台线程处理数据
                var mergedRows = await Task.Run(() =>
                {
                    // 按构件编号分组
                    return Assemblies.GroupBy(a => a.AssemblyNumber)
                        .Select((g, idx) => new MergedAssemblyRow
                        {
                            Index = idx + 1,
                            AssemblyNumber = g.Key,
                            Name = g.First().Name,
                            Profile = g.First().Profile,
                            Material = g.First().Material,
                            Finish = g.First().Finish,
                            Class = g.First().Class,
                            Phase = g.First().Phase,
                            Count = g.Count(),
                            PartCount = g.Sum(a => a.PartCount),
                            AssemblyPrefix = g.First().AssemblyPrefix,
                            AssemblyStartNumber = g.First().AssemblyStartNumber,
                            Guid = g.First().Guid,
                            ModelObjectIds = g.Select(a => a.ModelObjectId).ToList(),
                            Remark = g.First().Remark
                        }).ToList();
                });

                // 在UI线程更新集合
                MergedAssemblies.Clear();
                foreach (var row in mergedRows)
                {
                    MergedAssemblies.Add(row);
                }

                // 通知视图更新
                OnPropertyChanged(nameof(AssembliesView));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新合并行时发生错误: {ex.Message}");
            }
        }

        public void HandleAssemblySelectionChanged(IList<AssemblyInfo> selectedAssemblies)
        {
            // 直接高亮，不使用防抖动
            if (_mainViewModel != null && !_mainViewModel.EnableHighlight)
            {
                return; // 如果禁用了高亮功能，直接返回
            }

            try
            {
                if (selectedAssemblies == null || selectedAssemblies.Count == 0)
                {
                    // 如果没有选中任何构件，清除高亮
                    _teklaModelService.HighlightObjects(new List<TeklaModelPart>());
                    _lastHighlightedAssemblyNumbers.Clear();
                    _lastHighlightedModelObjectIds.Clear();
                    _lastHighlightedPartIds.Clear();
                    return;
                }

                // 获取构件编号列表
                var assemblyNumbers = selectedAssemblies.Select(a => a.AssemblyNumber).ToList();

                // 更新最后高亮的构件编号
                _lastHighlightedAssemblyNumbers = assemblyNumbers;

                // 限制同时处理的构件数量，避免卡顿
                const int maxAssembliesToProcess = 50;
                if (assemblyNumbers.Count > maxAssembliesToProcess)
                {
                    // 如果超过最大处理数量，只处理前50个
                    var limitedAssemblyNumbers = assemblyNumbers.Take(maxAssembliesToProcess).ToList();
                    // 使用缓存优化的高亮方法
                    HighlightAssembliesUsingCache(limitedAssemblyNumbers);
                    LogInfo($"选中了 {assemblyNumbers.Count} 个构件，但为了保持性能只高亮了前 {maxAssembliesToProcess} 个");
                }
                else
                {
                    // 使用缓存优化的高亮方法
                    HighlightAssembliesUsingCache(assemblyNumbers);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮构件时发生错误: {ex.Message}");
            }
        }

        // 使用缓存优化的构件高亮方法
        private void HighlightAssembliesUsingCache(List<string> assemblyNumbers)
        {
            // 标记正在高亮
            IsHighlighting = true;

            try
            {
                // 将字符串列表转换为AssemblyInfo列表进行高亮
                var assemblyInfos = assemblyNumbers.Select(an => new AssemblyInfo { AssemblyNumber = an }).ToList();
                _teklaModelService.HighlightAssembliesUsingCache(assemblyInfos);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮构件时发生错误: {ex.Message}");
            }
            finally
            {
                // 完成高亮
                IsHighlighting = false;
            }
        }

        // 使用缓存优化的构件高亮方法 - 使用构件ID版本
        private void HighlightAssembliesUsingCache(List<int> modelObjectIds)
        {
            // 标记正在高亮
            IsHighlighting = true;

            try
            {
                // 限制同时处理的构件数量
                const int maxObjectsToProcess = 50;
                if (modelObjectIds.Count > maxObjectsToProcess)
                {
                    // 如果超过最大处理数量，只处理前50个
                    var limitedModelObjectIds = modelObjectIds.Take(maxObjectsToProcess).ToList();
                    var limitedParts = limitedModelObjectIds.Select(id => new TeklaModelPart(0) { ModelObjectId = id }).ToList();
                    _teklaModelService.HighlightObjects(limitedParts);
                    LogInfo($"选中了 {modelObjectIds.Count} 个构件，但为了保持性能只高亮了前 {maxObjectsToProcess} 个");
                }
                else
                {
                    var parts = modelObjectIds.Select(id => new TeklaModelPart(0) { ModelObjectId = id }).ToList();
                    _teklaModelService.HighlightObjects(parts);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮构件时发生错误: {ex.Message}");
            }
            finally
            {
                // 完成高亮
                IsHighlighting = false;
            }
        }

        // 更新缓存的方法
        private void UpdateCache(string assemblyNumber, List<int> modelObjectIds)
        {
            if (!string.IsNullOrEmpty(assemblyNumber) && modelObjectIds != null && modelObjectIds.Count > 0)
            {
                // 更新构件编号到模型对象ID的缓存
                _assemblyNumberToModelObjectIdsCache[assemblyNumber] = modelObjectIds;
            }
        }

        // 尝试获取构件中的所有零件ID
        private void TryGetAssemblyParts()
        {
            try
            {
                // 这个方法可以在应用启动时或模型加载后调用
                // 它会尝试获取所有构件中的零件，并更新缓存

                // 由于这个操作可能比较耗时，在后台线程中执行
                Task.Run(async () =>
                {
                    // 使用字典记录已处理的构件编号，避免重复处理
                    var processedAssemblyNumbers = new HashSet<string>();

                    // 分批处理构件，避免长时间阻塞
                    const int batchSize = 20; // 每批处理的构件数量
                    var assemblyGroups = Assemblies
                        .GroupBy(a => a.AssemblyNumber)
                        .Select(g => new { AssemblyNumber = g.Key, ModelObjectIds = g.Select(a => a.ModelObjectId).ToList() })
                        .ToList();

                    for (int i = 0; i < assemblyGroups.Count; i += batchSize)
                    {
                        var batch = assemblyGroups.Skip(i).Take(batchSize).ToList();

                        foreach (var group in batch)
                        {
                            if (!processedAssemblyNumbers.Contains(group.AssemblyNumber) &&
                                !_assemblyNumberToModelObjectIdsCache.ContainsKey(group.AssemblyNumber))
                            {
                                // 更新缓存
                                UpdateCache(group.AssemblyNumber, group.ModelObjectIds);
                                processedAssemblyNumbers.Add(group.AssemblyNumber);

                                // 预加载构件的零件ID到高亮缓存
                                if (!_assemblyHighlightCache.ContainsKey(group.AssemblyNumber))
                                {
                                    try
                                    {
                                        // 尝试获取构件中的所有零件ID
                                        var partIds = _teklaModelService.GetPartIdsForAssembly(group.AssemblyNumber);
                                        if (partIds != null && partIds.Count > 0)
                                        {
                                            _assemblyHighlightCache[group.AssemblyNumber] = partIds;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"预加载构件 {group.AssemblyNumber} 的零件ID时出错: {ex.Message}");
                                    }
                                }
                            }
                        }

                        // 每处理一批，短暂暂停，避免占用过多资源
                        await Task.Delay(50);
                    }

                    System.Diagnostics.Debug.WriteLine($"预加载完成，共处理 {processedAssemblyNumbers.Count} 个不同构件");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"尝试获取构件零件时发生错误: {ex.Message}");
            }
        }

        // 记录日志的辅助方法
        private void LogInfo(string message)
        {
            _teklaModelService?.LogInfo(message);
        }

        public class MergedAssemblyRow
        {
            public int Index { get; set; }
            public string AssemblyNumber { get; set; }
            public string Name { get; set; }
            public string Profile { get; set; }
            public string Material { get; set; }
            public string Finish { get; set; }
            public string Class { get; set; }
            public string Phase { get; set; }
            public int Count { get; set; }
            public int PartCount { get; set; }
            public string AssemblyPrefix { get; set; }
            public string AssemblyStartNumber { get; set; }
            public string Guid { get; set; }
            public List<int> ModelObjectIds { get; set; }
            public string Remark { get; set; }
        }
    }
}

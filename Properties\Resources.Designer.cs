//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace TeklaList.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TeklaList.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 关于 的本地化字符串。
        /// </summary>
        internal static string About {
            get {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 TeklaList 的本地化字符串。
        /// </summary>
        internal static string AppName {
            get {
                return ResourceManager.GetString("AppName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tekla Structures 零件列表工具 的本地化字符串。
        /// </summary>
        internal static string AppDescription {
            get {
                return ResourceManager.GetString("AppDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 2.0.0 的本地化字符串。
        /// </summary>
        internal static string AppVersion {
            get {
                return ResourceManager.GetString("AppVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 应用程序已就绪 的本地化字符串。
        /// </summary>
        internal static string ApplicationReady {
            get {
                return ResourceManager.GetString("ApplicationReady", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 应用程序正在启动... 的本地化字符串。
        /// </summary>
        internal static string ApplicationStarting {
            get {
                return ResourceManager.GetString("ApplicationStarting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 构件 的本地化字符串。
        /// </summary>
        internal static string Assemblies {
            get {
                return ResourceManager.GetString("Assemblies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已加载 {0} 个构件 的本地化字符串。
        /// </summary>
        internal static string AssembliesLoaded {
            get {
                return ResourceManager.GetString("AssembliesLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 构件模式 的本地化字符串。
        /// </summary>
        internal static string AssemblyMode {
            get {
                return ResourceManager.GetString("AssemblyMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 构件号 的本地化字符串。
        /// </summary>
        internal static string AssemblyNumber {
            get {
                return ResourceManager.GetString("AssemblyNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 正在检查 Tekla 连接... 的本地化字符串。
        /// </summary>
        internal static string CheckingTeklaConnection {
            get {
                return ResourceManager.GetString("CheckingTeklaConnection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 清除 的本地化字符串。
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 清除筛选 的本地化字符串。
        /// </summary>
        internal static string ClearFilter {
            get {
                return ResourceManager.GetString("ClearFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 清除选择 的本地化字符串。
        /// </summary>
        internal static string ClearSelection {
            get {
                return ResourceManager.GetString("ClearSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已连接 的本地化字符串。
        /// </summary>
        internal static string Connected {
            get {
                return ResourceManager.GetString("Connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确认 的本地化字符串。
        /// </summary>
        internal static string Confirmation {
            get {
                return ResourceManager.GetString("Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 数量 的本地化字符串。
        /// </summary>
        internal static string Count {
            get {
                return ResourceManager.GetString("Count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未连接 的本地化字符串。
        /// </summary>
        internal static string Disconnected {
            get {
                return ResourceManager.GetString("Disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 错误 的本地化字符串。
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 导出数据 的本地化字符串。
        /// </summary>
        internal static string ExportData {
            get {
                return ResourceManager.GetString("ExportData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 筛选结果: {0} 项 的本地化字符串。
        /// </summary>
        internal static string FilteredItems {
            get {
                return ResourceManager.GetString("FilteredItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 表面处理 的本地化字符串。
        /// </summary>
        internal static string Finish {
            get {
                return ResourceManager.GetString("Finish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 帮助 的本地化字符串。
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 高亮显示时发生错误 的本地化字符串。
        /// </summary>
        internal static string HighlightError {
            get {
                return ResourceManager.GetString("HighlightError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 序号 的本地化字符串。
        /// </summary>
        internal static string Index {
            get {
                return ResourceManager.GetString("Index", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 信息 的本地化字符串。
        /// </summary>
        internal static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加载中... 的本地化字符串。
        /// </summary>
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 正在加载构件... 的本地化字符串。
        /// </summary>
        internal static string LoadingAssemblies {
            get {
                return ResourceManager.GetString("LoadingAssemblies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 正在加载零件... 的本地化字符串。
        /// </summary>
        internal static string LoadingParts {
            get {
                return ResourceManager.GetString("LoadingParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加载数据时发生错误 的本地化字符串。
        /// </summary>
        internal static string LoadError {
            get {
                return ResourceManager.GetString("LoadError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加载零件 的本地化字符串。
        /// </summary>
        internal static string LoadParts {
            get {
                return ResourceManager.GetString("LoadParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 主零件名称 的本地化字符串。
        /// </summary>
        internal static string MainPartName {
            get {
                return ResourceManager.GetString("MainPartName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 材质 的本地化字符串。
        /// </summary>
        internal static string Material {
            get {
                return ResourceManager.GetString("Material", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 合并零件 的本地化字符串。
        /// </summary>
        internal static string MergedParts {
            get {
                return ResourceManager.GetString("MergedParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 名称 的本地化字符串。
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未找到构件 的本地化字符串。
        /// </summary>
        internal static string NoAssembliesFound {
            get {
                return ResourceManager.GetString("NoAssembliesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未找到零件 的本地化字符串。
        /// </summary>
        internal static string NoPartsFound {
            get {
                return ResourceManager.GetString("NoPartsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 零件数量 的本地化字符串。
        /// </summary>
        internal static string PartCount {
            get {
                return ResourceManager.GetString("PartCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 零件模式 的本地化字符串。
        /// </summary>
        internal static string PartMode {
            get {
                return ResourceManager.GetString("PartMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 零件号 的本地化字符串。
        /// </summary>
        internal static string PartNumber {
            get {
                return ResourceManager.GetString("PartNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 零件 的本地化字符串。
        /// </summary>
        internal static string Parts {
            get {
                return ResourceManager.GetString("Parts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已加载 {0} 个零件 的本地化字符串。
        /// </summary>
        internal static string PartsLoaded {
            get {
                return ResourceManager.GetString("PartsLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 规格 的本地化字符串。
        /// </summary>
        internal static string Profile {
            get {
                return ResourceManager.GetString("Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 就绪 的本地化字符串。
        /// </summary>
        internal static string Ready {
            get {
                return ResourceManager.GetString("Ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 刷新 的本地化字符串。
        /// </summary>
        internal static string Refresh {
            get {
                return ResourceManager.GetString("Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 搜索零件... 的本地化字符串。
        /// </summary>
        internal static string SearchPlaceholder {
            get {
                return ResourceManager.GetString("SearchPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 全选 的本地化字符串。
        /// </summary>
        internal static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已选择: {0} 项 的本地化字符串。
        /// </summary>
        internal static string SelectedItems {
            get {
                return ResourceManager.GetString("SelectedItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置 的本地化字符串。
        /// </summary>
        internal static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Tekla Structures 已连接 的本地化字符串。
        /// </summary>
        internal static string TeklaConnected {
            get {
                return ResourceManager.GetString("TeklaConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 连接 Tekla Structures 时发生错误 的本地化字符串。
        /// </summary>
        internal static string TeklaConnectionError {
            get {
                return ResourceManager.GetString("TeklaConnectionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未连接到 Tekla Structures 的本地化字符串。
        /// </summary>
        internal static string TeklaNotConnected {
            get {
                return ResourceManager.GetString("TeklaNotConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 总计: {0} 项 的本地化字符串。
        /// </summary>
        internal static string TotalItems {
            get {
                return ResourceManager.GetString("TotalItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 警告 的本地化字符串。
        /// </summary>
        internal static string Warning {
            get {
                return ResourceManager.GetString("Warning", resourceCulture);
            }
        }
    }
}
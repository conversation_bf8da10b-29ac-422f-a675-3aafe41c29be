# TeklaList - Tekla Structures 零件列表工具

## 项目简介

TeklaList 是一个专为 Tekla Structures 19.0 设计的高性能零件列表工具，旨在解决原项目中构件模式和零件模式的卡顿问题，提供流畅的用户体验和高效的模型检查功能。

## 主要特性

### 🚀 性能优化
- **异步加载**: 采用异步编程模式，避免UI阻塞
- **智能缓存**: 实现多级缓存机制，显著提升数据加载速度
- **内存优化**: 优化内存使用，减少垃圾回收压力
- **并发处理**: 使用线程安全的数据结构，支持并发操作

### 🎯 功能特性
- **双模式支持**: 支持零件模式和构件模式切换
- **智能过滤**: 实时搜索和过滤功能
- **批量操作**: 支持批量选择和高亮
- **数据导出**: 支持多种格式的数据导出
- **模型同步**: 与Tekla模型实时同步

### 🎨 用户界面
- **现代化设计**: 采用现代化的WPF界面设计
- **响应式布局**: 自适应不同屏幕尺寸
- **主题支持**: 支持明暗主题切换
- **快捷键**: 丰富的键盘快捷键支持

### 🔧 技术改进
- **清晰高亮**: 优化模型高亮显示，不显示尺寸线
- **错误处理**: 完善的错误处理和日志记录
- **单例模式**: 防止多实例运行
- **自动恢复**: 窗口状态自动保存和恢复

## 系统要求

- **操作系统**: Windows 7/8/10/11 (64位)
- **Tekla Structures**: 19.0 或更高版本
- **.NET Framework**: 4.8 或更高版本
- **内存**: 建议 4GB 以上
- **硬盘空间**: 50MB 可用空间

## 安装说明

### 方式一：直接运行
1. 下载最新版本的 `TeklaList.exe`
2. 将程序放置在任意目录
3. 双击运行程序

### 方式二：从源码编译
1. 克隆或下载源代码
2. 使用 Visual Studio 2019 或更高版本打开 `TeklaList.sln`
3. 确保已安装 Tekla Structures 19.0
4. 编译并运行项目

## 使用指南

### 基本操作

1. **启动程序**
   - 确保 Tekla Structures 已运行并打开模型
   - 启动 TeklaList 程序
   - 程序会自动检测 Tekla 连接状态

2. **加载零件**
   - 点击 "加载零件" 按钮
   - 程序会异步加载当前模型中的所有零件
   - 加载进度会在状态栏显示

3. **切换模式**
   - 使用 "构件模式" 开关在零件模式和构件模式间切换
   - 零件模式：显示所有单独零件
   - 构件模式：按构件分组显示

4. **过滤和搜索**
   - 在搜索框中输入关键词进行实时过滤
   - 支持按零件名称、规格、材质等属性搜索
   - 使用清除按钮快速清空搜索条件

5. **选择和高亮**
   - 单击选择单个零件
   - Ctrl+单击多选零件
   - 选中的零件会在 Tekla 模型中高亮显示
   - 高亮显示已优化，不会显示尺寸线

### 高级功能

1. **数据刷新**
   - 点击 "刷新" 按钮更新数据
   - 程序会重新加载模型中的最新数据

2. **数据清除**
   - 点击 "清除" 按钮清空当前数据
   - 释放内存资源

3. **状态监控**
   - 状态栏显示连接状态、加载进度和统计信息
   - 实时监控程序运行状态

## 快捷键

- `F5`: 刷新数据
- `Ctrl+F`: 聚焦搜索框
- `Escape`: 清除搜索
- `Ctrl+A`: 全选
- `Delete`: 清除选择
- `F1`: 显示帮助

## 配置文件

程序会在以下位置保存配置文件：
- 用户设置：`%APPDATA%\TeklaList\Settings.json`
- 日志文件：`%APPDATA%\TeklaList\Logs\`
- 缓存文件：`%TEMP%\TeklaList\Cache\`

## 故障排除

### 常见问题

1. **无法连接到 Tekla Structures**
   - 确保 Tekla Structures 已启动并打开模型
   - 检查 Tekla Structures 版本是否为 19.0 或更高
   - 重启 TeklaList 程序

2. **程序运行缓慢**
   - 检查系统内存使用情况
   - 清除程序缓存
   - 重启程序

3. **高亮显示异常**
   - 检查 Tekla 模型视图设置
   - 确保模型中存在要高亮的零件
   - 刷新程序数据

4. **程序崩溃**
   - 查看日志文件获取详细错误信息
   - 确保系统满足最低要求
   - 联系技术支持

### 日志文件

程序会自动记录运行日志，日志文件位置：
```
%APPDATA%\TeklaList\Logs\TeklaList_YYYY-MM-DD.log
```

日志包含以下信息：
- 程序启动和退出记录
- 错误和异常信息
- 性能统计数据
- 用户操作记录

## 技术架构

### 项目结构
```
TeklaList/
├── Models/              # 数据模型
│   ├── TeklaModelPart.cs
│   └── TeklaAssembly.cs
├── Services/            # 业务服务
│   └── TeklaModelService.cs
├── ViewModels/          # 视图模型
│   ├── MainViewModel.cs
│   └── PartListViewModel.cs
├── Views/               # 用户界面
│   └── MainWindow.xaml
├── Controls/            # 自定义控件
│   └── FilterableDataGrid.xaml
├── Commands/            # 命令实现
│   └── RelayCommand.cs
├── Converters/          # 值转换器
│   └── BooleanToVisibilityConverter.cs
├── Utils/               # 工具类
│   └── Logger.cs
└── Resources/           # 资源文件
```

### 核心技术
- **WPF**: 用户界面框架
- **MVVM**: 架构模式
- **Async/Await**: 异步编程
- **ConcurrentDictionary**: 线程安全集合
- **SemaphoreSlim**: 并发控制
- **INotifyPropertyChanged**: 数据绑定
- **Tekla Open API**: Tekla Structures 集成

## 性能优化

### 内存优化
- 使用对象池减少内存分配
- 实现智能缓存策略
- 及时释放不需要的资源
- 优化数据结构设计

### 响应性优化
- 异步加载大量数据
- UI虚拟化技术
- 后台线程处理
- 智能更新策略

### 网络优化
- 批量API调用
- 数据压缩传输
- 连接池管理
- 超时处理机制

## 版本历史

### v2.0.0 (当前版本)
- 🎉 全面重构，性能大幅提升
- ✨ 新增构件模式支持
- 🚀 优化异步加载机制
- 🎨 全新现代化界面设计
- 🔧 修复高亮显示问题
- 📝 完善日志记录系统

### v1.0.0 (原始版本)
- 基础零件列表功能
- 简单的数据显示
- 基本的Tekla集成

## 开发团队

- **项目负责人**: TeklaList Team
- **开发语言**: C# / WPF
- **开发工具**: Visual Studio 2019+
- **版本控制**: Git

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系我们

如有问题或建议，请通过以下方式联系我们：

- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/teklalist/teklalist
- **官网**: https://www.teklalist.com

---

**感谢使用 TeklaList！** 🎉

我们致力于为 Tekla Structures 用户提供最优秀的零件列表工具。您的反馈和建议是我们持续改进的动力。
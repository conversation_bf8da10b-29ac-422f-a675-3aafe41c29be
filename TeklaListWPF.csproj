<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>TeklaList</RootNamespace>
    <AssemblyName>TeklaList</AssemblyName>
    <LangVersion>8.0</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <ApplicationIcon>Resources\icon.ico</ApplicationIcon>
    <StartupObject>TeklaList.App</StartupObject>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <NoWarn>CS8600;CS8601;CS8602;CS8603;CS8604;CS8618;CS8625</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  
  <!-- Tekla Structures 引用 - 如果Tekla未安装，这些引用将被忽略 -->
  <PropertyGroup Condition="Exists('C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll')">
    <DefineConstants>$(DefineConstants);TEKLA_AVAILABLE</DefineConstants>
  </PropertyGroup>
  
  <ItemGroup Condition="Exists('C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll')">
    <Reference Include="Tekla.Structures">
      <HintPath>C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Model">
      <HintPath>C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>

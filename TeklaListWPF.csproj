<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <RootNamespace>TeklaList</RootNamespace>
    <AssemblyName>TeklaList</AssemblyName>
    <LangVersion>8.0</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <ApplicationIcon>Resources\icon.ico</ApplicationIcon>
    <StartupObject>TeklaList.App</StartupObject>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <NoWarn>CS8600;CS8601;CS8602;CS8603;CS8604;CS8618;CS8625</NoWarn>
    <Version>2.0.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>TeklaList - Tekla Structures 零件列表工具</Product>
    <Description>专为 Tekla Structures 19.0 设计的高性能零件列表工具</Description>
    <Company>TeklaList Team</Company>
    <Copyright>Copyright © 2024 TeklaList Team</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Serilog" Version="2.12.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>

  <!-- Tekla Structures 引用 - 如果Tekla未安装，这些引用将被忽略 -->
  <PropertyGroup Condition="Exists('C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll')">
    <DefineConstants>$(DefineConstants);TEKLA_AVAILABLE</DefineConstants>
  </PropertyGroup>

  <ItemGroup Condition="Exists('C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll')">
    <Reference Include="Tekla.Structures">
      <HintPath>C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Model">
      <HintPath>C:\Program Files\Tekla Structures\19.0\nt\bin\Tekla.Structures.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>

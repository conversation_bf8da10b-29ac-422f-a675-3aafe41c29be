using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using TeklaList.Models;

namespace TeklaList.Services
{
    /// <summary>
    /// Tekla模型服务接口
    /// </summary>
    public interface ITeklaModelService : IDisposable
    {
        /// <summary>
        /// 检查Tekla连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 异步获取所有零件
        /// </summary>
        Task<List<TeklaModelPart>> GetAllPartsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步获取选中的零件
        /// </summary>
        Task<List<TeklaModelPart>> GetSelectedPartsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步获取指定构件的零件
        /// </summary>
        Task<List<TeklaModelPart>> GetAssemblyPartsAsync(string assemblyNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步获取构件信息列表
        /// </summary>
        Task<List<AssemblyInfo>> GetAssembliesAsync(List<TeklaModelPart> parts = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 高亮显示零件
        /// </summary>
        Task HighlightPartsAsync(IEnumerable<TeklaModelPart> parts, bool clearPrevious = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 高亮显示零件（通过ID）
        /// </summary>
        Task HighlightPartsByIdAsync(IEnumerable<int> partIds, bool clearPrevious = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 清除高亮
        /// </summary>
        Task ClearHighlightAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 选择零件
        /// </summary>
        Task SelectPartsAsync(IEnumerable<TeklaModelPart> parts, bool clearPrevious = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 清除选择
        /// </summary>
        Task ClearSelectionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 刷新模型数据
        /// </summary>
        Task RefreshModelAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 清除缓存
        /// </summary>
        void ClearCache();

        /// <summary>
        /// 获取模型统计信息
        /// </summary>
        Task<ModelStatistics> GetModelStatisticsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// 模型变更事件
        /// </summary>
        event EventHandler ModelChanged;
    }

    /// <summary>
    /// 模型统计信息
    /// </summary>
    public class ModelStatistics
    {
        /// <summary>
        /// 总零件数
        /// </summary>
        public int TotalParts { get; set; }

        /// <summary>
        /// 总构件数
        /// </summary>
        public int TotalAssemblies { get; set; }

        /// <summary>
        /// 不同材质数量
        /// </summary>
        public int UniqueMaterials { get; set; }

        /// <summary>
        /// 不同规格数量
        /// </summary>
        public int UniqueProfiles { get; set; }

        /// <summary>
        /// 总重量
        /// </summary>
        public double TotalWeight { get; set; }

        /// <summary>
        /// 模型边界框
        /// </summary>
        public BoundingBox? ModelBounds { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// 边界框
    /// </summary>
    public class BoundingBox
    {
        public double MinX { get; set; }
        public double MinY { get; set; }
        public double MinZ { get; set; }
        public double MaxX { get; set; }
        public double MaxY { get; set; }
        public double MaxZ { get; set; }

        public double Width => MaxX - MinX;
        public double Height => MaxY - MinY;
        public double Depth => MaxZ - MinZ;
    }
}

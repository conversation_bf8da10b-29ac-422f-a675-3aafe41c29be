using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace TeklaList
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static Mutex? _mutex;

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 添加全局异常处理
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
                DispatcherUnhandledException += App_DispatcherUnhandledException;
                TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

                base.OnStartup(e);

                // 检查单实例
                if (!CheckSingleInstance())
                {
                    Shutdown();
                    return;
                }

                // 创建并显示主窗口
                CreateMainWindow();
            }
            catch (Exception ex)
            {
                var errorMessage = $"应用程序启动失败: {ex.Message}";
                MessageBox.Show(errorMessage, "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 尝试记录错误
                try
                {
                    var logPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_startup_error.log");
                    File.AppendAllText(logPath, $"{DateTime.Now}: {errorMessage}\n{ex.StackTrace}\n\n");
                }
                catch { }

                Shutdown();
            }
        }



        private bool CheckSingleInstance()
        {
            bool createdNew = false;
            try
            {
                _mutex = new Mutex(true, "TeklaList_v2", out createdNew);
                if (!createdNew)
                {
                    Process currentProcess = Process.GetCurrentProcess();
                    var runningProcesses = Process.GetProcessesByName(currentProcess.ProcessName)
                        .Where(p => p.Id != currentProcess.Id);

                    if (runningProcesses.Any())
                    {
                        var result = MessageBox.Show(
                            "检测到已有TeklaList在运行。\n\n点击[是] - 继续使用当前版本\n点击[否] - 停止当前版本并使用已运行的版本",
                            "版本冲突提示",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Exclamation);

                        if (result == MessageBoxResult.Yes)
                        {
                            foreach (var process in runningProcesses)
                            {
                                try
                                {
                                    process.Kill();
                                }
                                catch (Exception ex)
                                {
                                    // 忽略进程终止错误
                                }
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show("启动程序时发生错误：" + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void CreateMainWindow()
        {
            // 简化的主窗口创建
            var mainWindow = new MainWindow();
            MainWindow = mainWindow;
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // 释放互斥锁
                if (_mutex != null)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                }
            }
            catch (Exception ex)
            {
                // 尝试记录退出错误
                try
                {
                    var logPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_exit_error.log");
                    File.AppendAllText(logPath, $"{DateTime.Now}: 退出时发生错误: {ex.Message}\n{ex.StackTrace}\n\n");
                }
                catch { }
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                string errorMessage = exception != null
                    ? $"未处理的异常: {exception.Message}\n\n{exception.StackTrace}"
                    : "发生未知异常";

                MessageBox.Show(errorMessage, "程序错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 严重错误，程序即将关闭\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
            finally
            {
                // 如果是终止性异常，程序将退出
                if (e.IsTerminating)
                {
                    MessageBox.Show("程序遇到严重错误，即将关闭。", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"UI线程未处理的异常: {e.Exception.Message}\n\n{e.Exception.StackTrace}";

                MessageBox.Show(errorMessage, "UI错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: UI线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }

        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"任务未处理的异常: {e.Exception.Message}\n\n{e.Exception.InnerException?.StackTrace}";

                MessageBox.Show(errorMessage, "任务错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已观察，防止应用程序崩溃
                e.SetObserved();
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 任务线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }
    }
}

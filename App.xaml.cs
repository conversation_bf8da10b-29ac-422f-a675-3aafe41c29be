using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TeklaList.Configuration;
using TeklaList.Services;
using TeklaList.Services.Logging;
using TeklaList.ViewModels;

namespace TeklaList
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static Mutex? _mutex;
        private IServiceProvider? _serviceProvider;
        private ILogger<App>? _logger;
        private ConfigurationManager? _configManager;

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 初始化服务容器
                InitializeServices();

                // 添加全局异常处理
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
                DispatcherUnhandledException += App_DispatcherUnhandledException;
                TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

                base.OnStartup(e);

                // 检查单实例
                if (!CheckSingleInstance())
                {
                    Shutdown();
                    return;
                }

                // 创建并显示主窗口
                CreateMainWindow();

                _logger?.LogInformation("TeklaList 应用程序启动成功");
            }
            catch (Exception ex)
            {
                var errorMessage = $"应用程序启动失败: {ex.Message}";
                MessageBox.Show(errorMessage, "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 尝试记录错误
                try
                {
                    var logPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_startup_error.log");
                    File.AppendAllText(logPath, $"{DateTime.Now}: {errorMessage}\n{ex.StackTrace}\n\n");
                }
                catch { }

                Shutdown();
            }
        }

        private void InitializeServices()
        {
            try
            {
                // 简化的初始化，暂时不使用DI
                // 这些字段暂时设为null，避免访问错误
                _serviceProvider = null;
                _logger = null;
                _configManager = null;

                // 初始化基本的日志系统（可选）
                try
                {
                    var logDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "TeklaList", "Logs");
                    Directory.CreateDirectory(logDir);
                    // LoggingService.Initialize(logDir); // 暂时注释掉
                }
                catch
                {
                    // 忽略日志初始化错误
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"服务初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private bool CheckSingleInstance()
        {
            bool createdNew = false;
            try
            {
                _mutex = new Mutex(true, "TeklaList_v2", out createdNew);
                if (!createdNew)
                {
                    Process currentProcess = Process.GetCurrentProcess();
                    var runningProcesses = Process.GetProcessesByName(currentProcess.ProcessName)
                        .Where(p => p.Id != currentProcess.Id);

                    if (runningProcesses.Any())
                    {
                        var result = MessageBox.Show(
                            "检测到已有TeklaList在运行。\n\n点击[是] - 继续使用当前版本\n点击[否] - 停止当前版本并使用已运行的版本",
                            "版本冲突提示",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Exclamation);

                        if (result == MessageBoxResult.Yes)
                        {
                            foreach (var process in runningProcesses)
                            {
                                try
                                {
                                    process.Kill();
                                    _logger?.LogInformation("已终止运行中的进程: {ProcessId}", process.Id);
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogWarning(ex, "无法终止进程: {ProcessId}", process.Id);
                                }
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "检查单实例时发生错误");
                MessageBox.Show("启动程序时发生错误：" + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void CreateMainWindow()
        {
            // 简化的主窗口创建
            var mainWindow = new MainWindow();
            MainWindow = mainWindow;
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _logger?.LogInformation("TeklaList 应用程序正在退出...");

                // 保存配置
                _configManager?.SaveSettings();

                // 释放服务
                if (_serviceProvider is IDisposable disposableProvider)
                {
                    disposableProvider.Dispose();
                }

                // 释放互斥锁
                if (_mutex != null)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                }

                // 关闭日志系统
                LoggingService.Shutdown();
            }
            catch (Exception ex)
            {
                // 尝试记录退出错误
                try
                {
                    var logPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_exit_error.log");
                    File.AppendAllText(logPath, $"{DateTime.Now}: 退出时发生错误: {ex.Message}\n{ex.StackTrace}\n\n");
                }
                catch { }
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                string errorMessage = exception != null
                    ? $"未处理的异常: {exception.Message}\n\n{exception.StackTrace}"
                    : "发生未知异常";

                _logger?.LogCritical(exception, "应用程序域未处理异常");

                MessageBox.Show(errorMessage, "程序错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 严重错误，程序即将关闭\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
            finally
            {
                // 如果是终止性异常，程序将退出
                if (e.IsTerminating)
                {
                    MessageBox.Show("程序遇到严重错误，即将关闭。", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"UI线程未处理的异常: {e.Exception.Message}\n\n{e.Exception.StackTrace}";
                _logger?.LogError(e.Exception, "UI线程未处理异常");

                MessageBox.Show(errorMessage, "UI错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: UI线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }

        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"任务未处理的异常: {e.Exception.Message}\n\n{e.Exception.InnerException?.StackTrace}";
                _logger?.LogError(e.Exception, "任务未处理异常");

                MessageBox.Show(errorMessage, "任务错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已观察，防止应用程序崩溃
                e.SetObserved();
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaList_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 任务线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }
    }
}
